<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

// Default route - redirect to login
$routes->get('/', 'Auth::login');

// Authentication routes
$routes->group('auth', function($routes) {
    $routes->get('login', 'Auth::login');
    $routes->post('attemptLogin', 'Auth::attemptLogin');
    $routes->get('logout', 'Auth::logout');
    $routes->get('register', 'Auth::register');
    $routes->get('forgot-password', 'Auth::forgotPassword');
    $routes->get('check', 'Auth::checkAuth');
});

// Dashboard routes (protected by AuthFilter)
$routes->group('dashboard', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Dashboard::index');
    $routes->get('profile', 'Dashboard::profile');
    $routes->post('updateProfile', 'Dashboard::updateProfile');
    $routes->get('change-password', 'Dashboard::changePassword');
    $routes->post('updatePassword', 'Dashboard::updatePassword');

    // User management routes (Admin only) - RESTful approach
    $routes->get('users', 'Dashboard::users');                      // GET /dashboard/users - List all users
    $routes->get('users/create', 'Dashboard::createUser');          // GET /dashboard/users/create - Show create form
    $routes->post('users', 'Dashboard::storeUser');                 // POST /dashboard/users - Store new user
    $routes->get('users/(:num)', 'Dashboard::showUser/$1');         // GET /dashboard/users/{id} - Show user details
    $routes->get('users/(:num)/edit', 'Dashboard::editUser/$1');    // GET /dashboard/users/{id}/edit - Show edit form
    $routes->put('users/(:num)', 'Dashboard::updateUser/$1');       // PUT /dashboard/users/{id} - Update user
    $routes->patch('users/(:num)', 'Dashboard::updateUser/$1');     // PATCH /dashboard/users/{id} - Update user
    $routes->delete('users/(:num)', 'Dashboard::destroyUser/$1');   // DELETE /dashboard/users/{id} - Delete user
});

// Invoice routes (protected) - RESTful approach
$routes->group('invoices', ['filter' => 'auth'], static function ($routes) {
    $routes->get('/', 'Invoice::index');                    // GET /invoices - List all invoices
    $routes->get('create', 'Invoice::create');              // GET /invoices/create - Show create form
    $routes->post('/', 'Invoice::store');                   // POST /invoices - Store new invoice
    $routes->post('generate-ai-data', 'Invoice::generateAiData'); // POST /invoices/generate-ai-data - AI data generation
    $routes->get('refresh-csrf', 'Invoice::refreshCsrf'); // GET /invoices/refresh-csrf - Get fresh CSRF token
    $routes->get('(:num)', 'Invoice::show/$1');             // GET /invoices/{id} - Show invoice details
    $routes->get('(:num)/edit', 'Invoice::edit/$1');        // GET /invoices/{id}/edit - Show edit form
    $routes->put('(:num)', 'Invoice::update/$1');           // PUT /invoices/{id} - Update invoice
    $routes->patch('(:num)', 'Invoice::update/$1');         // PATCH /invoices/{id} - Update invoice
    $routes->delete('(:num)', 'Invoice::destroy/$1');       // DELETE /invoices/{id} - Delete invoice
    $routes->post('(:num)/convert', 'Invoice::convertToInvoice/$1'); // POST /invoices/{id}/convert - Convert quotation to invoice
    $routes->get('(:num)/export-pdf', 'Invoice::exportPDF/$1'); // GET /invoices/{id}/export-pdf - Export invoice as PDF
});

// Account routes (protected) - RESTful approach
$routes->group('accounts', ['filter' => 'auth'], static function ($routes) {
    $routes->get('/', 'Account::index');                    // GET /accounts - List all accounts
    $routes->get('create', 'Account::create');              // GET /accounts/create - Show create form
    $routes->post('/', 'Account::store');                   // POST /accounts - Store new account
    $routes->get('(:num)', 'Account::show/$1');             // GET /accounts/{id} - Show account details
    $routes->get('(:num)/edit', 'Account::edit/$1');        // GET /accounts/{id}/edit - Show edit form
    $routes->put('(:num)', 'Account::update/$1');           // PUT /accounts/{id} - Update account
    $routes->patch('(:num)', 'Account::update/$1');         // PATCH /accounts/{id} - Update account
    $routes->delete('(:num)', 'Account::destroy/$1');       // DELETE /accounts/{id} - Delete account
    $routes->post('(:num)/set-default', 'Account::setDefault/$1'); // POST /accounts/{id}/set-default - Set as default
});

// Client routes (protected) - RESTful approach
$routes->group('clients', ['filter' => 'auth'], static function ($routes) {
    $routes->get('/', 'Client::index');                     // GET /clients - List all clients
    $routes->get('create', 'Client::create');               // GET /clients/create - Show create form
    $routes->post('/', 'Client::store');                    // POST /clients - Store new client
    $routes->get('search', 'Client::search');               // GET /clients/search - AJAX search endpoint
    $routes->get('(:num)', 'Client::show/$1');              // GET /clients/{id} - Show client details
    $routes->get('(:num)/edit', 'Client::edit/$1');         // GET /clients/{id}/edit - Show edit form
    $routes->put('(:num)', 'Client::update/$1');            // PUT /clients/{id} - Update client
    $routes->patch('(:num)', 'Client::update/$1');          // PATCH /clients/{id} - Update client
    $routes->delete('(:num)', 'Client::destroy/$1');        // DELETE /clients/{id} - Delete client
    $routes->post('(:num)/restore', 'Client::restore/$1');  // POST /clients/{id}/restore - Restore deleted client
});

// Legacy home route (redirect to dashboard if logged in, otherwise to login)
$routes->get('home', 'Home::index');

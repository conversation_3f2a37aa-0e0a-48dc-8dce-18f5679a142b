# Dakoii Accounts - Admin Template System

This document explains the unified admin template system created for the Dakoii Accounts application.

## Overview

The template system provides a consistent, professional interface across all admin pages with:
- Unified header with logo and user information
- Responsive sidebar navigation
- Consistent styling and components
- Flash message handling
- Breadcrumb support
- Mobile-responsive design

## Template Files

### 1. `admin_template.php`
The main admin template that provides the base layout for all admin pages.

**Features:**
- Complete HTML structure with head, header, sidebar, and main content area
- Bootstrap 5 integration
- Font Awesome icons
- Inter font family
- Custom CSS variables for consistent theming
- Responsive design
- Flash message display
- Breadcrumb navigation
- JavaScript utilities

**Usage:**
```php
<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<!-- Your page content here -->
<?= $this->endSection() ?>
```

### 2. `dashboard_template.php`
A specialized template that extends the admin template with dashboard-specific styling.

**Features:**
- All features of admin_template
- Additional dashboard-specific CSS for feature boxes, cards, and layouts
- Special styling for quick access sections
- Dashboard grid layouts

**Usage:**
```php
<?= $this->extend('templates/dashboard_template') ?>

<?= $this->section('dashboard_content') ?>
<!-- Your dashboard content here -->
<?= $this->endSection() ?>
```

## Template Sections

### Available Sections

1. **`content`** - Main page content area
2. **`dashboard_content`** - Dashboard-specific content (only in dashboard_template)
3. **`styles`** - Additional CSS styles
4. **`scripts`** - Additional JavaScript

### Example Usage

```php
<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    /* Custom page styles */
    .custom-class { color: red; }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="page-header">
    <h1 class="page-title">Page Title</h1>
    <p class="page-subtitle">Page description</p>
</div>

<div class="admin-card">
    <div class="admin-card-body">
        <!-- Page content -->
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Custom page JavaScript
</script>
<?= $this->endSection() ?>
```

## CSS Classes and Components

### Layout Classes
- `.page-header` - Page header container
- `.page-title` - Main page title
- `.page-subtitle` - Page subtitle/description
- `.page-actions` - Action buttons container

### Card Components
- `.admin-card` - Main card container
- `.admin-card-header` - Card header
- `.admin-card-title` - Card title
- `.admin-card-body` - Card content area

### Status Badges
- `.status-badge` - Base status badge
- `.status-active` - Active status (green)
- `.status-inactive` - Inactive status (gray)
- `.status-draft` - Draft status (yellow)
- `.status-sent` - Sent status (blue)
- `.status-paid` - Paid status (green)
- `.status-overdue` - Overdue status (red)

### Statistics
- `.stat-icon` - Statistics icon container
- `.stat-number` - Statistics number
- `.stat-label` - Statistics label

## Color Variables

The template uses CSS custom properties for consistent theming:

```css
:root {
    --primary-color: #2e8b57;      /* Main green */
    --primary-light: #32cd32;      /* Light green */
    --primary-dark: #228b22;       /* Dark green */
    --secondary-color: #6c757d;    /* Gray */
    --success-color: #28a745;      /* Success green */
    --danger-color: #dc3545;       /* Error red */
    --warning-color: #ffc107;      /* Warning yellow */
    --info-color: #17a2b8;         /* Info blue */
    --light-color: #f8f9fa;        /* Light background */
    --dark-color: #343a40;         /* Dark text */
}
```

## Navigation

The sidebar navigation automatically highlights the current page based on the `$current_page` variable passed to the view.

**Available navigation items:**
- `dashboard` - Dashboard home
- `invoices` - Invoice management
- `accounts` - Account management
- `clients` - Client management
- `reports` - Reports
- `users` - User management (admin only)

## Flash Messages

The template automatically displays flash messages:
- `success` - Success messages (green)
- `error` - Error messages (red)
- `warning` - Warning messages (yellow)
- `info` - Information messages (blue)

Messages auto-hide after 5 seconds.

## Breadcrumbs

Pass breadcrumb data to display navigation breadcrumbs:

```php
$data['breadcrumbs'] = [
    ['title' => 'Dashboard', 'url' => base_url('dashboard')],
    ['title' => 'Invoices', 'url' => base_url('invoices')],
    ['title' => 'Create Invoice'] // No URL = current page
];
```

## Migration from Old Templates

All view files have been updated to use the new template system:

### Before:
```php
return view('layouts/admin_layout', [
    'content' => view('invoice/invoice_index', $data),
    'title' => $data['title'],
    'current_page' => 'invoices'
]);
```

### After:
```php
return view('invoice/invoice_index', $data);
```

The view file now extends the template:
```php
<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<!-- Content here -->
<?= $this->endSection() ?>
```

## Best Practices

1. **Always extend the appropriate template** - Use `admin_template` for most pages, `dashboard_template` for dashboard pages
2. **Use consistent CSS classes** - Follow the established naming conventions
3. **Include page metadata** - Set `title` and `current_page` in controller data
4. **Use semantic HTML** - Follow proper HTML structure within content sections
5. **Test responsiveness** - Ensure your content works on mobile devices
6. **Follow the color scheme** - Use the CSS variables for consistent theming

## File Structure

```
app/Views/templates/
├── admin_template.php      # Main admin template
├── dashboard_template.php  # Dashboard-specific template
└── README.md              # This documentation

app/Views/
├── dashboard/
│   ├── dashboard_index_new.php
│   ├── dashboard_profile_new.php
│   ├── dashboard_users_new.php
│   └── dashboard_change_password_new.php
├── invoice/
│   ├── invoice_index.php
│   ├── invoice_create.php
│   ├── invoice_edit.php
│   └── invoice_view.php
├── account/
│   ├── account_index.php
│   ├── account_create.php
│   ├── account_edit.php
│   └── account_view.php
└── client/
    ├── client_index.php
    ├── client_create.php
    ├── client_edit.php
    └── client_view.php
```

<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddInvoiceItemsJsonField extends Migration
{
    public function up()
    {
        // Add invoice_items JSON field to store line items as JSON data
        $this->forge->addColumn('invoices', [
            'invoice_items' => [
                'type' => 'JSON',
                'null' => true,
                'after' => 'ai_generated',
                'comment' => 'Invoice line items stored as JSON array'
            ]
        ]);
    }

    public function down()
    {
        // Remove the invoice_items JSON field
        $this->forge->dropColumn('invoices', 'invoice_items');
    }
}

<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">Account Management</h1>
        <p class="page-subtitle">Manage organizational accounts for invoice and quotation generation</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('accounts/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            Add New Account
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon">
                    <i class="fas fa-building text-primary"></i>
                </div>
                <h3 class="stat-number"><?= $total_accounts ?></h3>
                <p class="stat-label">Total Accounts</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon">
                    <i class="fas fa-check-circle text-success"></i>
                </div>
                <h3 class="stat-number"><?= $active_accounts ?></h3>
                <p class="stat-label">Active Accounts</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon">
                    <i class="fas fa-star text-warning"></i>
                </div>
                <h3 class="stat-number"><?= $default_account ? '1' : '0' ?></h3>
                <p class="stat-label">Default Account</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon">
                    <i class="fas fa-pause-circle text-secondary"></i>
                </div>
                <h3 class="stat-number"><?= $total_accounts - $active_accounts ?></h3>
                <p class="stat-label">Inactive Accounts</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="admin-card mb-4">
    <div class="admin-card-body">
        <form method="GET" action="<?= base_url('accounts') ?>" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Search by trading name, account name, or bank..." 
                       value="<?= esc($filters['search'] ?? '') ?>">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="">All Status</option>
                    <option value="active" <?= ($filters['status'] ?? '') === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= ($filters['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search"></i>
                    Filter
                </button>
                <a href="<?= base_url('accounts') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Accounts Table -->
<div class="admin-card">
    <div class="admin-card-header">
        <h5 class="admin-card-title">Accounts List</h5>
    </div>
    <div class="admin-card-body">
        <?php if (!empty($accounts)): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Trading Name</th>
                            <th>Account Details</th>
                            <th>Bank Information</th>
                            <th>Status</th>
                            <th>Default</th>
                            <th>Created</th>
                            <th width="150">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($accounts as $account): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if (!empty($account['logo'])): ?>
                                            <img src="<?= base_url($account['logo']) ?>" alt="Logo" class="me-2" style="width: 32px; height: 32px; object-fit: cover; border-radius: 4px;">
                                        <?php else: ?>
                                            <div class="me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; background-color: #f8f9fa; border-radius: 4px;">
                                                <i class="fas fa-building text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <strong><?= esc($account['trading_name']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= esc($account['account_name']) ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>Account:</strong> <?= esc($account['account_number']) ?><br>
                                        <?php if (!empty($account['email'])): ?>
                                            <small class="text-muted">
                                                <i class="fas fa-envelope"></i> <?= esc($account['email']) ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= esc($account['bank']) ?></strong><br>
                                        <?php if (!empty($account['bank_branch'])): ?>
                                            <small class="text-muted"><?= esc($account['bank_branch']) ?></small>
                                        <?php endif; ?>
                                        <?php if (!empty($account['swift_code'])): ?>
                                            <br><small class="text-muted">SWIFT: <?= esc($account['swift_code']) ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge status-<?= $account['status'] ?>">
                                        <?= ucfirst($account['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($account['is_default']): ?>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-star"></i> Default
                                        </span>
                                    <?php else: ?>
                                        <form method="POST" action="<?= base_url('accounts/' . $account['id'] . '/set-default') ?>" style="display: inline;">
                                            <?= csrf_field() ?>
                                            <button type="submit" class="btn btn-sm btn-outline-warning"
                                                    onclick="return confirm('Set this account as default?')"
                                                    title="Set as Default">
                                                <i class="fas fa-star"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= date('M d, Y', strtotime($account['created_at'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('accounts/' . $account['id']) ?>" 
                                           class="btn btn-sm btn-outline-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('accounts/' . $account['id'] . '/edit') ?>" 
                                           class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if (!$account['is_default']): ?>
                                            <form method="POST" action="<?= base_url('accounts/' . $account['id']) ?>" style="display: inline;">
                                                <?= csrf_field() ?>
                                                <input type="hidden" name="_method" value="DELETE">
                                                <button type="submit" class="btn btn-sm btn-outline-danger"
                                                        onclick="return confirm('Are you sure you want to delete this account?')"
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($pager): ?>
                <div class="d-flex justify-content-center mt-4">
                    <?= $pager->links() ?>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No accounts found</h5>
                <p class="text-muted">Start by creating your first organizational account.</p>
                <a href="<?= base_url('accounts/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Create First Account
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Auto-submit form on filter change
document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});

// Clear search on escape key
document.getElementById('search').addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        this.value = '';
        this.form.submit();
    }
});
</script>
<?= $this->endSection() ?>

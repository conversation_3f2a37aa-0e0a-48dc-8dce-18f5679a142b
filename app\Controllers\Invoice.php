<?php

namespace App\Controllers;

use App\Models\InvoiceModel;
use App\Models\ClientModel;
use App\Models\AccountModel;
use CodeIgniter\HTTP\RedirectResponse;

class Invoice extends BaseController
{
    protected $invoiceModel;
    protected $clientModel;
    protected $accountModel;

    public function __construct()
    {
        $this->invoiceModel = new InvoiceModel();
        $this->clientModel = new ClientModel();
        $this->accountModel = new AccountModel();
    }

    /**
     * US-INV-002: View Invoice List
     * Display paginated list of invoices and quotations with filtering
     */
    public function index()
    {
        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login');
        }

        $data = [
            'title' => 'Invoice Management',
            'current_page' => 'invoices',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => base_url('dashboard')],
                ['title' => 'Invoice Management']
            ]
        ];

        // Get filters from request
        $filters = [
            'type' => $this->request->getGet('type'),
            'status' => $this->request->getGet('status'),
            'client_id' => $this->request->getGet('client_id'),
            'search' => $this->request->getGet('search'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to')
        ];

        // Get invoices with pagination
        $perPage = 20;
        $invoicesData = $this->invoiceModel->getInvoicesWithClient($filters, $perPage);
        
        $data['invoices'] = $invoicesData['data'];
        $data['pager'] = $invoicesData['pager'];
        $data['filters'] = $filters;

        // Get clients for filter dropdown
        $data['clients'] = $this->clientModel->getClientOptions();

        // Get statistics
        $data['stats'] = $this->invoiceModel->getDashboardStats();

        return view('invoice/invoice_index', $data);
    }

    /**
     * US-INV-001: Create New Invoice
     * Show form for creating new invoice or quotation
     */
    public function create()
    {
        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login');
        }

        $data = [
            'title' => 'Create Invoice',
            'current_page' => 'invoices',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => base_url('dashboard')],
                ['title' => 'Invoice Management', 'url' => base_url('invoices')],
                ['title' => 'Create Invoice']
            ]
        ];

        // Get clients and accounts for dropdown
        $data['clients'] = $this->clientModel->getClientOptions();
        $data['accounts'] = $this->accountModel->getActiveAccountsForDropdown();

        // Check if there's AI-generated data in session
        $aiGeneratedData = session()->getFlashdata('ai_generated_data');

        if ($aiGeneratedData) {
            // Use AI-generated data
            $data['invoice'] = array_merge([
                'invoice_type' => $this->request->getGet('type') ?? 'invoice',
                'issue_date' => date('Y-m-d'),
                'due_date' => date('Y-m-d', strtotime('+30 days')),
                'tax_rate' => 0,
                'discount_amount' => 0,
                'status' => 'draft'
            ], $aiGeneratedData);

            // Use AI-generated items if available
            $data['items'] = $aiGeneratedData['items'] ?? [
                ['description' => '', 'quantity' => 1, 'unit_price' => 0]
            ];
        } else {
            // Set default values
            $data['invoice'] = [
                'invoice_type' => $this->request->getGet('type') ?? 'invoice',
                'issue_date' => date('Y-m-d'),
                'due_date' => date('Y-m-d', strtotime('+30 days')),
                'tax_rate' => 0,
                'discount_amount' => 0,
                'status' => 'draft'
            ];

            $data['items'] = [
                ['description' => '', 'quantity' => 1, 'unit_price' => 0]
            ];
        }

        return view('invoice/invoice_create', $data);
    }

    /**
     * US-INV-001: Store New Invoice (RESTful approach)
     * Process form submission for creating new invoice
     * Only handles POST requests
     */
    public function store()
    {
        // Ensure this method only handles POST requests
        if (!$this->request->is('post')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }

        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login');
        }


        // Get form data
        $invoiceData = [
            'invoice_type' => $this->request->getPost('invoice_type'),
            'client_id' => $this->request->getPost('client_id'),
            'issue_date' => $this->request->getPost('issue_date'),
            'due_date' => $this->request->getPost('due_date'),
            'tax_rate' => floatval($this->request->getPost('tax_rate') ?? 0),
            'discount_amount' => floatval($this->request->getPost('discount_amount') ?? 0),
            'status' => $this->request->getPost('status') ?? 'draft',
            'notes' => $this->request->getPost('notes'),
            'terms_conditions' => $this->request->getPost('terms_conditions')
        ];

        // Get items data
        $items = [];
        $descriptions = $this->request->getPost('item_description') ?? [];
        $quantities = $this->request->getPost('item_quantity') ?? [];
        $unitPrices = $this->request->getPost('item_unit_price') ?? [];

        for ($i = 0; $i < count($descriptions); $i++) {
            if (!empty($descriptions[$i]) && !empty($quantities[$i]) && isset($unitPrices[$i])) {
                $items[] = [
                    'description' => $descriptions[$i],
                    'quantity' => floatval($quantities[$i]),
                    'unit_price' => floatval($unitPrices[$i])
                ];
            }
        }

        // Validate that we have at least one item
        if (empty($items)) {
            return redirect()->back()->withInput()->with('error', 'At least one invoice item is required.');
        }

        // Create invoice with items
        $invoiceId = $this->invoiceModel->createInvoiceWithItems($invoiceData, $items);

        if ($invoiceId) {
            $type = $invoiceData['invoice_type'] === 'quotation' ? 'quotation' : 'invoice';
            session()->setFlashdata('success', ucfirst($type) . ' created successfully.');
            return redirect()->to('/invoices/' . $invoiceId);
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create invoice. Please check your input and try again.');
        }
    }

    /**
     * Display single invoice
     */
    public function show($id)
    {
        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login');
        }

        $invoice = $this->invoiceModel->getInvoiceWithDetails($id);

        if (!$invoice) {
            session()->setFlashdata('error', 'Invoice not found.');
            return redirect()->to('/invoices');
        }

        // Check if user can access this invoice
        if (session()->get('role') !== 'admin' && $invoice['user_id'] != session()->get('user_id')) {
            session()->setFlashdata('error', 'You do not have permission to view this invoice.');
            return redirect()->to('/invoices');
        }

        $data = [
            'title' => 'Invoice #' . $invoice['invoice_number'],
            'current_page' => 'invoices',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => base_url('dashboard')],
                ['title' => 'Invoice Management', 'url' => base_url('invoices')],
                ['title' => 'Invoice #' . $invoice['invoice_number']]
            ],
            'invoice' => $invoice
        ];

        return view('invoice/invoice_view', $data);
    }

    /**
     * US-INV-003: Edit Existing Invoice
     * Show form for editing existing invoice
     */
    public function edit($id)
    {
        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login');
        }

        $invoice = $this->invoiceModel->getInvoiceWithDetails($id);

        if (!$invoice) {
            session()->setFlashdata('error', 'Invoice not found.');
            return redirect()->to('/invoices');
        }

        // Check if user can edit this invoice
        if (session()->get('role') !== 'admin' && $invoice['user_id'] != session()->get('user_id')) {
            session()->setFlashdata('error', 'You do not have permission to edit this invoice.');
            return redirect()->to('/invoices');
        }

        // Check if invoice can be edited (not paid)
        if ($invoice['status'] === 'paid') {
            session()->setFlashdata('error', 'Paid invoices cannot be edited.');
            return redirect()->to('/invoices/' . $id);
        }

        $data = [
            'title' => 'Edit Invoice #' . $invoice['invoice_number'],
            'current_page' => 'invoices',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => base_url('dashboard')],
                ['title' => 'Invoice Management', 'url' => base_url('invoices')],
                ['title' => 'Edit Invoice #' . $invoice['invoice_number']]
            ],
            'invoice' => $invoice,
            'items' => $invoice['items']
        ];

        // Get clients and accounts for dropdown
        $data['clients'] = $this->clientModel->getClientOptions();
        $data['accounts'] = $this->accountModel->getActiveAccountsForDropdown();

        return view('invoice/invoice_edit', $data);
    }

    /**
     * US-INV-003: Update Existing Invoice (RESTful approach)
     * Process form submission for updating invoice
     * Only handles PUT/PATCH requests
     */
    public function update($id)
    {
        // Ensure this method only handles PUT/PATCH requests
        if (!$this->request->is('put') && !$this->request->is('patch')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }

        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login');
        }


        $invoice = $this->invoiceModel->find($id);

        if (!$invoice) {
            session()->setFlashdata('error', 'Invoice not found.');
            return redirect()->to('/invoices');
        }

        // Check permissions
        if (session()->get('role') !== 'admin' && $invoice['user_id'] != session()->get('user_id')) {
            session()->setFlashdata('error', 'You do not have permission to edit this invoice.');
            return redirect()->to('/invoices');
        }

        // Check if invoice can be edited
        if ($invoice['status'] === 'paid') {
            session()->setFlashdata('error', 'Paid invoices cannot be edited.');
            return redirect()->to('/invoices/' . $id);
        }

        // Get form data
        $invoiceData = [
            'invoice_type' => $this->request->getPost('invoice_type'),
            'client_id' => $this->request->getPost('client_id'),
            'issue_date' => $this->request->getPost('issue_date'),
            'due_date' => $this->request->getPost('due_date'),
            'tax_rate' => floatval($this->request->getPost('tax_rate') ?? 0),
            'discount_amount' => floatval($this->request->getPost('discount_amount') ?? 0),
            'status' => $this->request->getPost('status') ?? 'draft',
            'notes' => $this->request->getPost('notes'),
            'terms_conditions' => $this->request->getPost('terms_conditions')
        ];

        // Get items data
        $items = [];
        $descriptions = $this->request->getPost('item_description') ?? [];
        $quantities = $this->request->getPost('item_quantity') ?? [];
        $unitPrices = $this->request->getPost('item_unit_price') ?? [];

        for ($i = 0; $i < count($descriptions); $i++) {
            if (!empty($descriptions[$i]) && !empty($quantities[$i]) && isset($unitPrices[$i])) {
                $items[] = [
                    'description' => $descriptions[$i],
                    'quantity' => floatval($quantities[$i]),
                    'unit_price' => floatval($unitPrices[$i])
                ];
            }
        }

        // Validate that we have at least one item
        if (empty($items)) {
            return redirect()->back()->withInput()->with('error', 'At least one invoice item is required.');
        }

        // Update invoice with items
        if ($this->invoiceModel->updateInvoiceWithItems($id, $invoiceData, $items)) {
            $type = $invoiceData['invoice_type'] === 'quotation' ? 'quotation' : 'invoice';
            session()->setFlashdata('success', ucfirst($type) . ' updated successfully.');
            return redirect()->to('/invoices/' . $id);
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update invoice. Please check your input and try again.');
        }
    }

    /**
     * US-INV-004: Delete Invoice (RESTful destroy method)
     * Soft delete an invoice
     * Only handles DELETE requests
     */
    public function destroy($id)
    {
        // Ensure this method only handles DELETE requests
        if (!$this->request->is('delete')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }

        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login');
        }

        $invoice = $this->invoiceModel->find($id);

        if (!$invoice) {
            session()->setFlashdata('error', 'Invoice not found.');
            return redirect()->to('/invoices');
        }

        // Check permissions
        if (session()->get('role') !== 'admin' && $invoice['user_id'] != session()->get('user_id')) {
            session()->setFlashdata('error', 'You do not have permission to delete this invoice.');
            return redirect()->to('/invoices');
        }

        // Check if invoice can be deleted (only draft status)
        if ($invoice['status'] !== 'draft') {
            session()->setFlashdata('error', 'Only draft invoices can be deleted.');
            return redirect()->to('/invoices/' . $id);
        }

        // Soft delete the invoice
        if ($this->invoiceModel->delete($id)) {
            $type = $invoice['invoice_type'] === 'quotation' ? 'quotation' : 'invoice';
            session()->setFlashdata('success', ucfirst($type) . ' deleted successfully.');
        } else {
            session()->setFlashdata('error', 'Failed to delete invoice.');
        }

        return redirect()->to('/invoices');
    }

    /**
     * Refresh CSRF token endpoint
     * Used to get a fresh CSRF token after AI Assistant usage
     */
    public function refreshCsrf()
    {
        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ]);
        }

        // Check if request is AJAX
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request method'
            ]);
        }

        // Generate fresh CSRF token
        helper('form');
        $csrfName = csrf_token();
        $csrfHash = csrf_hash();

        return $this->response->setJSON([
            'success' => true,
            'csrf_name' => $csrfName,
            'csrf_token' => $csrfHash
        ]);
    }



    /**
     * Generate invoice data using AI Assistant (Standard form submission)
     */
    public function generateAiData()
    {
        // Ensure this method only handles POST requests
        if (!$this->request->is('post')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }

        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login');
        }

        // Get form data
        $prompt = $this->request->getPost('prompt');
        $clientHint = $this->request->getPost('client_hint') ?? '';
        $invoiceType = $this->request->getPost('invoice_type') ?? 'invoice';
        $returnUrl = $this->request->getPost('return_url') ?? base_url('invoices/create');

        if (empty($prompt)) {
            session()->setFlashdata('error', 'Prompt is required for AI Assistant');
            return redirect()->to($returnUrl);
        }

        try {
            // Debug logging
            log_message('info', 'AI Assistant request started - Prompt length: ' . strlen($prompt));

            // Get available clients for context
            $clients = $this->clientModel->getClientOptions();
            $clientsList = [];
            foreach ($clients as $id => $name) {
                $clientsList[] = ['id' => $id, 'name' => $name];
            }

            // Prepare the AI prompt
            $systemPrompt = $this->buildSystemPrompt($clientsList, $invoiceType);
            $userPrompt = $this->buildUserPrompt($prompt, $clientHint);

            // Debug logging
            log_message('info', 'AI Assistant calling Grok API...');

            // Call the AI API
            $aiResponse = $this->callGrokAPI($systemPrompt, $userPrompt);
            
            if (!$aiResponse) {
                log_message('error', 'AI Assistant: callGrokAPI returned null');
                session()->setFlashdata('error', 'Failed to get response from AI service');
                return redirect()->to($returnUrl);
            }

            log_message('info', 'AI Assistant: Received response, length: ' . strlen($aiResponse));

            // Parse the AI response
            $invoiceData = $this->parseAIResponse($aiResponse);

            if (!$invoiceData) {
                log_message('error', 'AI Assistant: parseAIResponse returned null for response: ' . substr($aiResponse, 0, 200));
                session()->setFlashdata('error', 'Failed to parse AI response');
                return redirect()->to($returnUrl);
            }

            log_message('info', 'AI Assistant: Successfully generated invoice data');

            // Store the generated data in session to populate the form
            session()->setFlashdata('ai_generated_data', $invoiceData);
            session()->setFlashdata('success', 'Invoice data generated successfully by AI Assistant!');

            return redirect()->to($returnUrl);

        } catch (\Exception $e) {
            log_message('error', 'AI Assistant Error: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while processing your request');
            return redirect()->to($returnUrl);
        }
    }

    /**
     * Build system prompt for AI
     */
    private function buildSystemPrompt(array $clients, string $invoiceType): string
    {
        $clientsContext = '';
        if (!empty($clients)) {
            $clientsContext = "\n\nAvailable clients in the system:\n";
            foreach ($clients as $client) {
                $clientsContext .= "- ID: {$client['id']}, Name: {$client['name']}\n";
            }
        }

        return "You are an AI assistant helping to generate {$invoiceType} data. Based on the user's description, generate structured invoice data in JSON format.

Return ONLY a valid JSON object with the following structure:
{
    \"client_id\": null or matching client ID from the list,
    \"issue_date\": \"YYYY-MM-DD\" (today's date),
    \"due_date\": \"YYYY-MM-DD\" (typically 30 days from issue date),
    \"tax_rate\": number (percentage, e.g., 10 for 10%),
    \"discount_amount\": number (dollar amount),
    \"notes\": \"string\",
    \"terms_conditions\": \"string\",
    \"items\": [
        {
            \"description\": \"Item description\",
            \"quantity\": number,
            \"unit_price\": number
        }
    ]
}

Important rules:
1. If a client name is mentioned, try to match it with the available clients list. If found, use the client_id, otherwise set to null.
2. Generate realistic and detailed item descriptions.
3. Calculate appropriate pricing based on the context.
4. Include standard terms and conditions relevant to the service/product type.
5. Set appropriate tax rates (0-15% typically).
6. Set discount_amount to 0 unless specifically mentioned.
7. Use today's date (" . date('Y-m-d') . ") as issue_date.
8. Set due_date based on payment terms mentioned or default to 30 days from issue_date.
{$clientsContext}";
    }

    /**
     * Build user prompt for AI
     */
    private function buildUserPrompt(string $prompt, string $clientHint): string
    {
        $userPrompt = "Generate invoice/quotation data based on this description:\n\n{$prompt}";
        
        if (!empty($clientHint)) {
            $userPrompt .= "\n\nClient name hint: {$clientHint}";
        }
        
        $userPrompt .= "\n\nPlease return only the JSON object as specified in the system prompt.";
        
        return $userPrompt;
    }

    /**
     * Call Grok AI API
     */
    private function callGrokAPI(string $systemPrompt, string $userPrompt): ?string
    {
        $apiKey = '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';
        $url = 'https://openrouter.ai/api/v1/chat/completions';
        
        $data = [
            'model' => 'x-ai/grok-4-fast',
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $systemPrompt
                ],
                [
                    'role' => 'user',
                    'content' => $userPrompt
                ]
            ],
            'temperature' => 0.7,
            'max_tokens' => 2000
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Add this for debugging
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        // Enhanced logging for debugging
        if ($curlError) {
            log_message('error', 'Grok API cURL Error: ' . $curlError);
            return null;
        }
        
        if ($httpCode !== 200) {
            log_message('error', 'Grok API Error: HTTP ' . $httpCode . ' - ' . $response);
            return null;
        }
        
        if (empty($response)) {
            log_message('error', 'Grok API returned empty response');
            return null;
        }
        
        $responseData = json_decode($response, true);
        
        if (!$responseData) {
            log_message('error', 'Failed to decode Grok API JSON response: ' . $response);
            return null;
        }
        
        if (!isset($responseData['choices'][0]['message']['content'])) {
            log_message('error', 'Invalid Grok API response structure: ' . json_encode($responseData));
            return null;
        }
        
        return $responseData['choices'][0]['message']['content'];
    }

    /**
     * Parse AI response and validate structure
     */
    private function parseAIResponse(string $response): ?array
    {
        // Try to extract JSON from response (in case AI adds extra text)
        $response = trim($response);
        
        // Look for JSON object
        $start = strpos($response, '{');
        $end = strrpos($response, '}');
        
        if ($start === false || $end === false) {
            return null;
        }
        
        $jsonStr = substr($response, $start, $end - $start + 1);
        $data = json_decode($jsonStr, true);
        
        if (!$data) {
            return null;
        }
        
        // Validate required fields
        if (!isset($data['items']) || !is_array($data['items']) || empty($data['items'])) {
            return null;
        }
        
        // Set defaults for missing fields
        $defaults = [
            'client_id' => null,
            'issue_date' => date('Y-m-d'),
            'due_date' => date('Y-m-d', strtotime('+30 days')),
            'tax_rate' => 0,
            'discount_amount' => 0,
            'notes' => '',
            'terms_conditions' => ''
        ];
        
        foreach ($defaults as $key => $defaultValue) {
            if (!isset($data[$key])) {
                $data[$key] = $defaultValue;
            }
        }
        
        // Validate and clean items
        $cleanItems = [];
        foreach ($data['items'] as $item) {
            if (isset($item['description'], $item['quantity'], $item['unit_price'])) {
                $cleanItems[] = [
                    'description' => trim($item['description']),
                    'quantity' => max(0.01, floatval($item['quantity'])),
                    'unit_price' => max(0, floatval($item['unit_price']))
                ];
            }
        }
        
        if (empty($cleanItems)) {
            return null;
        }
        
        $data['items'] = $cleanItems;
        
        return $data;
    }

    /**
     * US-INV-005: Convert Quotation to Invoice
     * Convert a quotation to an invoice
     */
    public function convertToInvoice($id)
    {
        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login');
        }

        $quotation = $this->invoiceModel->find($id);

        if (!$quotation) {
            session()->setFlashdata('error', 'Quotation not found.');
            return redirect()->to('/invoices');
        }

        // Check permissions
        if (session()->get('role') !== 'admin' && $quotation['user_id'] != session()->get('user_id')) {
            session()->setFlashdata('error', 'You do not have permission to convert this quotation.');
            return redirect()->to('/invoices');
        }

        // Check if it's a quotation
        if ($quotation['invoice_type'] !== 'quotation') {
            session()->setFlashdata('error', 'Only quotations can be converted to invoices.');
            return redirect()->to('/invoices/' . $id);
        }

        // Check if quotation can be converted
        if (!in_array($quotation['status'], ['sent', 'viewed', 'accepted'])) {
            session()->setFlashdata('error', 'Only sent, viewed, or accepted quotations can be converted to invoices.');
            return redirect()->to('/invoices/' . $id);
        }

        // Convert quotation to invoice
        $invoiceId = $this->invoiceModel->convertQuotationToInvoice($id);

        if ($invoiceId) {
            session()->setFlashdata('success', 'Quotation converted to invoice successfully.');
            return redirect()->to('/invoices/' . $invoiceId);
        } else {
            session()->setFlashdata('error', 'Failed to convert quotation to invoice.');
            return redirect()->to('/invoices/' . $id);
        }
    }

    /**
     * US-INV-006: Generate PDF
     * Export invoice/quotation as PDF document
     * Implements direct download approach following the PDF export guide
     */
    public function exportPDF($id)
    {
        // Check authentication
        if (!session()->get('isLoggedIn')) {
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ]);
        }

        // Validate invoice ID
        if (!$id || !is_numeric($id)) {
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Valid invoice ID is required'
            ]);
        }

        try {
            // Fetch invoice with all details
            $invoice = $this->invoiceModel->getInvoiceWithDetails($id);

            if (!$invoice) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invoice not found'
                ]);
            }

            // Check permissions
            if (session()->get('role') !== 'admin' && $invoice['user_id'] != session()->get('user_id')) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'You do not have permission to export this invoice'
                ]);
            }

            // Generate and output PDF directly to browser
            $this->generateInvoicePDF($invoice);

            // No return needed - PDF is sent directly to browser

        } catch (\Exception $e) {
            log_message('error', 'PDF Export Error: ' . $e->getMessage());
            log_message('error', 'PDF Export Stack Trace: ' . $e->getTraceAsString());
            
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to generate PDF: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Private helper method to generate invoice PDF
     * Implements the standardized PDF generation pattern with Dakoii branding
     */
    private function generateInvoicePDF($invoice)
    {
        try {
            // Create TCPDF with custom footer for Dakoii Accounts System
            $pdf = new class('P', 'mm', 'A4', true, 'UTF-8', false) extends \TCPDF {
                public function Footer() {
                    $this->SetY(-18);
                    $footerY = $this->GetY();
                    $footerHeight = 15;
                    $footerWidth = $this->getPageWidth() - 20; // Account for margins
                    
                    // Draw Dakoii brand colored sections (based on company colors)
                    $this->SetFillColor(0, 123, 191); // Dakoii Blue
                    $this->Rect(10, $footerY, $footerWidth / 3, $footerHeight, 'F');
                    
                    $this->SetFillColor(52, 58, 64); // Dark Gray
                    $this->Rect(10 + ($footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');
                    
                    $this->SetFillColor(40, 167, 69); // Success Green
                    $this->Rect(10 + (2 * $footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');
                    
                    // Add footer content
                    $this->SetFont('helvetica', '', 8);
                    $this->SetY($footerY + 1);
                    
                    // Row 1
                    $this->SetTextColor(255, 255, 255);
                    $this->SetX(12);
                    $this->Cell(60, 3, 'Generated by Dakoii Accounts v1.0', 0, 0, 'L');
                    
                    $this->SetX(75);
                    $this->Cell(60, 3, 'Professional Invoice Management', 0, 0, 'C');
                    
                    $this->SetTextColor(255, 255, 255);
                    $this->SetX(140);
                    $this->Cell(60, 3, 'Generated: ' . date('M d, Y H:i') . ' | Page ' . $this->getAliasNumPage() . '/' . $this->getAliasNbPages(), 0, 0, 'R');
                    
                    // Row 2
                    $this->SetY($this->GetY() + 3);
                    $this->SetTextColor(255, 255, 255);
                    $this->SetX(12);
                    $this->Cell(60, 3, 'AI-Powered System', 0, 0, 'L');
                    
                    $this->SetX(75);
                    $this->Cell(60, 3, 'Developed by Dakoii Systems', 0, 0, 'C');
                    
                    // Row 3
                    $this->SetY($this->GetY() + 3);
                    $this->SetX(10);
                    $this->Cell($footerWidth, 3, 'www.dakoiims.com', 0, 0, 'C');
                    
                    $this->SetTextColor(0, 0, 0);
                }
            };

            // Configure PDF
            $pdf->SetCreator('Dakoii Accounts System');
            $pdf->SetAuthor('Noland Gande');
            $pdf->SetTitle(ucfirst($invoice['invoice_type']) . ' #' . $invoice['invoice_number']);
            $pdf->SetSubject('Invoice/Quotation Document');
            $pdf->SetKeywords('Invoice, Quotation, Dakoii, Accounts');
            
            // Set margins and page setup - reduced top margin for more content area
            $pdf->SetMargins(15, 15, 15);
            $pdf->SetAutoPageBreak(true, 25);
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(true);
            
            // Add page
            $pdf->AddPage();
            
            // Add logo header - use account-specific logo from database
            $logoPath = null;
            
            // Check if account has a logo
            if (!empty($invoice['logo'])) {
                // Account logo from database
                $logoPath = FCPATH . ltrim($invoice['logo'], '/');
            }
            
            // Fallback to system logo if account logo not available
            if (!$logoPath || !file_exists($logoPath)) {
                $logoPath = FCPATH . 'assets/system_img/system-logo.png';
            }
            
            // Display logo if available
            if ($logoPath && file_exists($logoPath)) {
                try {
                    $pdf->Image($logoPath, ($pdf->getPageWidth() - 40) / 2, $pdf->GetY(), 40, 20, '', '', '', false, 300, '', false, false, 0, false, false, false);
                    $pdf->Ln(25);
                } catch (\Exception $e) {
                    log_message('error', 'Logo Error in PDF: ' . $e->getMessage());
                    // Continue without logo if there's an error
                    $pdf->Ln(5);
                }
            } else {
                // No logo available, just add some space
                $pdf->Ln(5);
            }
            
            // Add title
            $pdf->SetFont('helvetica', 'B', 18);
            $documentType = ucfirst($invoice['invoice_type']);
            $pdf->Cell(0, 10, $documentType . ' #' . $invoice['invoice_number'], 0, 1, 'C');
            $pdf->Ln(5);
            
            // Add invoice content
            $this->addInvoiceContent($pdf, $invoice);
            
            // Generate filename and output directly to browser
            $filename = strtolower($invoice['invoice_type']) . '_' . $invoice['invoice_number'] . '_' . date('Y-m-d_H-i-s') . '.pdf';
            $pdf->Output($filename, 'D'); // 'D' = Direct download to browser
            
        } catch (\Exception $e) {
            log_message('error', 'PDF Generation Error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Add invoice content to PDF
     */
    private function addInvoiceContent($pdf, $invoice)
    {
        $pageWidth = $pdf->getPageWidth() - 30; // Account for margins
        
        // Invoice details section
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Document Details', 0, 1, 'L');
        $pdf->Ln(2);
        
        // Two-column layout for invoice details
        $colWidth = $pageWidth / 2;
        
        $pdf->SetFont('helvetica', '', 10);
        
        // Left column - Invoice info (removed status as requested)
        $y = $pdf->GetY();
        $pdf->SetXY(15, $y);
        $pdf->MultiCell($colWidth - 5, 5, 
            "Issue Date: " . date('F j, Y', strtotime($invoice['issue_date'])) . "\n" .
            "Due Date: " . ($invoice['due_date'] ? date('F j, Y', strtotime($invoice['due_date'])) : 'N/A') . "\n" .
            "Account: " . ($invoice['trading_name'] ?? 'Default Account') . "\n" .
            "Email: " . ($invoice['account_email'] ?? 'N/A'),
            0, 'L', 0, 1
        );
        
        // Right column - Client info
        $pdf->SetXY(15 + $colWidth, $y);
        $pdf->MultiCell($colWidth - 5, 5,
            "Bill To:\n" .
            $invoice['client_name'] . "\n" .
            ($invoice['client_email'] ?? '') . "\n" .
            ($invoice['client_phone'] ?? '') . "\n" .
            ($invoice['client_address'] ?? ''),
            0, 'L', 0, 1
        );
        
        $pdf->Ln(10);
        
        // Items table
        $this->addItemsTable($pdf, $invoice);
        
        // Add banking details section
        $this->addBankingDetails($pdf, $invoice);
        
        // Add stamp, QR code, and signature section
        $this->addStampAndSignatureSection($pdf, $invoice);
        
        // Add notes if present
        if (!empty($invoice['notes'])) {
            $pdf->Ln(5);
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 8, 'Notes', 0, 1, 'L');
            $pdf->SetFont('helvetica', '', 10);
            $pdf->MultiCell(0, 5, $invoice['notes'], 0, 'L');
        }
        
        // Add terms if present
        if (!empty($invoice['terms_conditions'])) {
            $pdf->Ln(5);
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 8, 'Terms & Conditions', 0, 1, 'L');
            $pdf->SetFont('helvetica', '', 10);
            $pdf->MultiCell(0, 5, $invoice['terms_conditions'], 0, 'L');
        }
    }
    
    /**
     * Add items table to PDF
     */
    private function addItemsTable($pdf, $invoice)
    {
        $pageWidth = $pdf->getPageWidth() - 30;
        
        // Table headers
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->SetFillColor(240, 240, 240);
        
        // Column widths
        $descWidth = $pageWidth * 0.5;
        $qtyWidth = $pageWidth * 0.15;
        $priceWidth = $pageWidth * 0.175;
        $totalWidth = $pageWidth * 0.175;
        
        // Headers
        $pdf->Cell($descWidth, 8, 'Description', 1, 0, 'L', true);
        $pdf->Cell($qtyWidth, 8, 'Quantity', 1, 0, 'C', true);
        $pdf->Cell($priceWidth, 8, 'Unit Price', 1, 0, 'R', true);
        $pdf->Cell($totalWidth, 8, 'Total', 1, 1, 'R', true);
        
        // Items
        $pdf->SetFont('helvetica', '', 9);
        $pdf->SetFillColor(255, 255, 255);
        
        if (!empty($invoice['invoice_items'])) {
            foreach ($invoice['invoice_items'] as $item) {
                // Calculate row height based on description length
                $lines = $pdf->getNumLines($item['description'], $descWidth - 2);
                $rowHeight = max(6, $lines * 4);
                
                $y = $pdf->GetY();
                
                // Check if we need a new page
                if ($y + $rowHeight > $pdf->getPageHeight() - 40) {
                    $pdf->AddPage();
                    
                    // Redraw headers
                    $pdf->SetFont('helvetica', 'B', 10);
                    $pdf->SetFillColor(240, 240, 240);
                    $pdf->Cell($descWidth, 8, 'Description', 1, 0, 'L', true);
                    $pdf->Cell($qtyWidth, 8, 'Quantity', 1, 0, 'C', true);
                    $pdf->Cell($priceWidth, 8, 'Unit Price', 1, 0, 'R', true);
                    $pdf->Cell($totalWidth, 8, 'Total', 1, 1, 'R', true);
                    
                    $pdf->SetFont('helvetica', '', 9);
                    $pdf->SetFillColor(255, 255, 255);
                    $y = $pdf->GetY();
                }
                
                // Description
                $pdf->SetXY(15, $y);
                $pdf->MultiCell($descWidth, $rowHeight, $item['description'], 1, 'L', false, 0);
                
                // Quantity
                $pdf->SetXY(15 + $descWidth, $y);
                $pdf->Cell($qtyWidth, $rowHeight, number_format($item['quantity'], 2), 1, 0, 'C');
                
                // Unit Price
                $pdf->SetXY(15 + $descWidth + $qtyWidth, $y);
                $pdf->Cell($priceWidth, $rowHeight, '$' . number_format($item['unit_price'], 2), 1, 0, 'R');
                
                // Total
                $pdf->SetXY(15 + $descWidth + $qtyWidth + $priceWidth, $y);
                $pdf->Cell($totalWidth, $rowHeight, '$' . number_format($item['line_total'], 2), 1, 0, 'R');
                
                $pdf->SetY($y + $rowHeight);
            }
        }
        
        // Totals section
        $pdf->Ln(5);
        $this->addTotalsSection($pdf, $invoice, $pageWidth);
    }
    
    /**
     * Add totals section to PDF
     */
    private function addTotalsSection($pdf, $invoice, $pageWidth)
    {
        $pdf->SetFont('helvetica', '', 10);
        
        // Position totals on the right side
        $totalsWidth = 80;
        $labelWidth = 40;
        $valueWidth = 40;
        $startX = 15 + $pageWidth - $totalsWidth;
        
        $y = $pdf->GetY();
        
        // Subtotal
        $pdf->SetXY($startX, $y);
        $pdf->Cell($labelWidth, 6, 'Subtotal:', 0, 0, 'R');
        $pdf->Cell($valueWidth, 6, '$' . number_format($invoice['subtotal'], 2), 0, 1, 'R');
        
        // Discount if applicable
        if ($invoice['discount_amount'] > 0) {
            $y = $pdf->GetY();
            $pdf->SetXY($startX, $y);
            $pdf->Cell($labelWidth, 6, 'Discount:', 0, 0, 'R');
            $pdf->Cell($valueWidth, 6, '-$' . number_format($invoice['discount_amount'], 2), 0, 1, 'R');
        }
        
        // Tax if applicable
        if ($invoice['tax_amount'] > 0) {
            $y = $pdf->GetY();
            $pdf->SetXY($startX, $y);
            $pdf->Cell($labelWidth, 6, 'Tax (' . number_format($invoice['tax_rate'], 1) . '%):', 0, 0, 'R');
            $pdf->Cell($valueWidth, 6, '$' . number_format($invoice['tax_amount'], 2), 0, 1, 'R');
        }
        
        // Total
        $pdf->SetFont('helvetica', 'B', 12);
        $y = $pdf->GetY() + 2;
        $pdf->SetXY($startX, $y);
        $pdf->Cell($labelWidth, 8, 'Total:', 1, 0, 'R', true);
        $pdf->Cell($valueWidth, 8, '$' . number_format($invoice['total_amount'], 2), 1, 1, 'R', true);
    }
    
    /**
     * Add banking details section to PDF
     */
    private function addBankingDetails($pdf, $invoice)
    {
        // Only add banking section if we have banking information
        if (empty($invoice['account_number']) && empty($invoice['bank']) && empty($invoice['swift_code'])) {
            return;
        }
        
        $pdf->Ln(8);
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Banking Details', 0, 1, 'L');
        $pdf->Ln(2);
        
        $pageWidth = $pdf->getPageWidth() - 30;
        $colWidth = $pageWidth / 2;
        
        $pdf->SetFont('helvetica', '', 10);
        
        // Left column
        $y = $pdf->GetY();
        $pdf->SetXY(15, $y);
        $bankingInfo = "";
        if (!empty($invoice['account_name'])) {
            $bankingInfo .= "Account Name: " . $invoice['account_name'] . "\n";
        }
        if (!empty($invoice['account_number'])) {
            $bankingInfo .= "Account Number: " . $invoice['account_number'] . "\n";
        }
        $pdf->MultiCell($colWidth - 5, 5, $bankingInfo, 0, 'L', 0, 1);
        
        // Right column
        $pdf->SetXY(15 + $colWidth, $y);
        $bankInfo = "";
        if (!empty($invoice['bank'])) {
            $bankInfo .= "Bank: " . $invoice['bank'] . "\n";
        }
        if (!empty($invoice['bank_branch'])) {
            $bankInfo .= "Branch: " . $invoice['bank_branch'] . "\n";
        }
        if (!empty($invoice['swift_code'])) {
            $bankInfo .= "SWIFT Code: " . $invoice['swift_code'] . "\n";
        }
        $pdf->MultiCell($colWidth - 5, 5, $bankInfo, 0, 'L', 0, 1);
    }
    
    /**
     * Add stamp, QR code, and signature section to PDF
     * Layout: Stamp | Signature | QR Code (no labels)
     */
    private function addStampAndSignatureSection($pdf, $invoice)
    {
        $pdf->Ln(10);
        
        $pageWidth = $pdf->getPageWidth() - 30;
        $colWidth = $pageWidth / 3;
        $y = $pdf->GetY();
        
        // Column 1: Company Stamp (no label)
        $stampY = $y;
        if (!empty($invoice['stamp'])) {
            $stampPath = FCPATH . ltrim($invoice['stamp'], '/');
            if (file_exists($stampPath)) {
                try {
                    $pdf->Image($stampPath, 15 + ($colWidth - 40) / 2, $stampY, 40, 25, '', '', '', false, 300);
                } catch (\Exception $e) {
                    log_message('error', 'Stamp Error in PDF: ' . $e->getMessage());
                }
            }
        }
        
        // Column 2: Signature (moved from column 3, no label)
        $sigY = $y;
        if (!empty($invoice['signature'])) {
            $sigPath = FCPATH . ltrim($invoice['signature'], '/');
            if (file_exists($sigPath)) {
                try {
                    $pdf->Image($sigPath, 15 + $colWidth + ($colWidth - 40) / 2, $sigY, 40, 20, '', '', '', false, 300);
                } catch (\Exception $e) {
                    log_message('error', 'Signature Error in PDF: ' . $e->getMessage());
                }
            }
        }
        
        // Column 3: QR Code (moved from column 2, no label)
        $qrY = $y;
        $this->addQRCode($pdf, $invoice, 15 + 2 * $colWidth + ($colWidth - 25) / 2, $qrY, 25);
        
        // Add some space after the section
        $pdf->SetY($y + 35);
    }
    
    /**
     * Add QR Code to PDF
     */
    private function addQRCode($pdf, $invoice, $x, $y, $size)
    {
        try {
            // Create QR code data
            $verifyUrl = base_url('invoices/' . $invoice['id']);
            $qrText = "Invoice: " . $invoice['invoice_number'] . "\n";
            $qrText .= "Amount: $" . number_format($invoice['total_amount'], 2) . "\n";
            $qrText .= "Client: " . $invoice['client_name'] . "\n";
            $qrText .= "Verify: " . $verifyUrl;
            
            // Try to generate actual QR code using a simpler approach
            try {
                // Use TCPDF's built-in 2D barcode if available
                $style = array(
                    'border' => 0,
                    'vpadding' => 'auto',
                    'hpadding' => 'auto',
                    'fgcolor' => array(0,0,0),
                    'bgcolor' => false, 
                    'module_width' => 1,
                    'module_height' => 1
                );
                
                // Try TCPDF 2D barcode first
                if (method_exists($pdf, 'write2DBarcode')) {
                    $pdf->write2DBarcode($qrText, 'QRCODE,L', $x, $y, $size, $size, $style, 'N');
                } else {
                    // Fallback to styled text representation
                    $pdf->Rect($x, $y, $size, $size, 'D'); // Draw border
                    $pdf->SetFont('helvetica', 'B', 6);
                    $pdf->SetXY($x + 1, $y + 2);
                    $pdf->MultiCell($size - 2, 2, "QR CODE\n" . $invoice['invoice_number'] . "\n$" . number_format($invoice['total_amount'], 2) . "\nScan to verify", 0, 'C');
                }
            } catch (\Exception $qrError) {
                log_message('error', 'QR Code Generation Error: ' . $qrError->getMessage());
                // Fallback to styled text representation
                $pdf->Rect($x, $y, $size, $size, 'D'); // Draw border
                $pdf->SetFont('helvetica', 'B', 6);
                $pdf->SetXY($x + 1, $y + 2);
                $pdf->MultiCell($size - 2, 2, "QR CODE\n" . $invoice['invoice_number'] . "\n$" . number_format($invoice['total_amount'], 2) . "\nScan to verify", 0, 'C');
            }
            
        } catch (\Exception $e) {
            log_message('error', 'QR Code Error in PDF: ' . $e->getMessage());
            // Fallback to text representation
            $pdf->SetXY($x, $y);
            $pdf->SetFont('helvetica', 'B', 8);
            $pdf->MultiCell($size, 3, "QR CODE\n" . $invoice['invoice_number'] . "\n$" . number_format($invoice['total_amount'], 2), 1, 'C');
        }
    }
}

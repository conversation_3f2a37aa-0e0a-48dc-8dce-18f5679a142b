<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class AuthFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return RequestInterface|ResponseInterface|string|void
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $session = session();
        
        // Check if user is logged in
        if (!$session->get('isLoggedIn')) {
            // Store the intended URL for redirect after login
            $session->set('redirect_url', current_url());
            
            // Set flash message for user feedback
            $session->setFlashdata('error', 'Please log in to access this page.');
            
            // Redirect to login page
            return redirect()->to('/auth/login');
        }
        
        // Check if user account is still active
        $userId = $session->get('user_id');
        if ($userId) {
            $userModel = new \App\Models\UsersModel();
            $user = $userModel->find($userId);
            
            if (!$user || $user['status'] !== 'active') {
                // User account is inactive or deleted, destroy session
                $session->destroy();
                $session->setFlashdata('error', 'Your account has been deactivated. Please contact administrator.');
                return redirect()->to('/auth/login');
            }
        }
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return ResponseInterface|void
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Nothing to do here for authentication
    }
}

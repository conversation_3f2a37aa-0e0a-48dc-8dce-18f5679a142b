<?php

namespace App\Controllers;

use App\Models\UsersModel;
use CodeIgniter\Controller;

class Auth extends BaseController
{
    protected $userModel;
    protected $session;

    public function __construct()
    {
        $this->userModel = new UsersModel();
        $this->session = session();
        helper(['form', 'url']);
    }

    /**
     * Display login form
     */
    public function login()
    {
        // If user is already logged in, redirect to dashboard
        if ($this->session->get('isLoggedIn')) {
            return redirect()->to('/dashboard');
        }

        $data = [
            'title' => 'Login - Dakoii Accounts',
            'validation' => $this->validator
        ];

        return view('welcome_message', $data);
    }

    /**
     * Process login form submission
     */
    public function attemptLogin()
    {
        // Validation rules for login
        $rules = [
            'email' => [
                'label' => 'Email',
                'rules' => 'required|valid_email',
                'errors' => [
                    'required' => 'Email is required.',
                    'valid_email' => 'Please enter a valid email address.'
                ]
            ],
            'password' => [
                'label' => 'Password',
                'rules' => 'required',
                'errors' => [
                    'required' => 'Password is required.'
                ]
            ]
        ];

        if (!$this->validate($rules)) {
            $data = [
                'title' => 'Login - Dakoii Accounts',
                'validation' => $this->validator
            ];
            return view('welcome_message', $data);
        }

        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');
        $remember = $this->request->getPost('remember');

        // Verify credentials
        $user = $this->userModel->verifyCredentials($email, $password);

        if ($user) {
            // Set session data
            $sessionData = [
                'user_id' => $user['id'],
                'email' => $user['email'],
                'first_name' => $user['first_name'],
                'last_name' => $user['last_name'],
                'role' => $user['role'],
                'isLoggedIn' => true
            ];

            $this->session->set($sessionData);

            // Handle remember me functionality
            if ($remember) {
                // Set a longer session timeout (30 days)
                $this->session->setTempdata('remember_login', true, 30 * 24 * 60 * 60);
            }

            // Set success message
            $this->session->setFlashdata('success', 'Welcome back, ' . $user['first_name'] . '!');

            // Redirect to intended page or dashboard
            $redirectUrl = $this->session->get('redirect_url') ?? '/dashboard';
            $this->session->remove('redirect_url');
            
            return redirect()->to($redirectUrl);
        } else {
            // Login failed
            $this->session->setFlashdata('error', 'Invalid email or password. Please try again.');
            
            $data = [
                'title' => 'Login - Dakoii Accounts',
                'validation' => $this->validator,
                'old_email' => $email
            ];
            
            return view('welcome_message', $data);
        }
    }

    /**
     * Logout user and destroy session
     */
    public function logout()
    {
        // Get user name for goodbye message
        $firstName = $this->session->get('first_name');
        
        // Destroy session
        $this->session->destroy();
        
        // Set logout message
        session()->setFlashdata('success', 'You have been logged out successfully. Goodbye' . ($firstName ? ', ' . $firstName : '') . '!');
        
        return redirect()->to('/auth/login');
    }

    /**
     * Check if user is logged in (for AJAX requests)
     */
    public function checkAuth()
    {
        $response = [
            'isLoggedIn' => $this->session->get('isLoggedIn') ?? false,
            'user' => null
        ];

        if ($response['isLoggedIn']) {
            $response['user'] = [
                'id' => $this->session->get('user_id'),
                'email' => $this->session->get('email'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
                'role' => $this->session->get('role')
            ];
        }

        return $this->response->setJSON($response);
    }

    /**
     * Display registration form (if needed in future)
     */
    public function register()
    {
        // For now, redirect to login
        // This can be implemented later if user registration is needed
        return redirect()->to('/auth/login');
    }

    /**
     * Handle forgot password (placeholder for future implementation)
     */
    public function forgotPassword()
    {
        // Placeholder for forgot password functionality
        $this->session->setFlashdata('info', 'Forgot password functionality will be implemented soon. Please contact administrator.');
        return redirect()->to('/auth/login');
    }
}

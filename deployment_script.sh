#!/bin/bash

# DAccounts Production Deployment Script
# This script deploys the daccounts application to the production server

echo "=== DAccounts Production Deployment Script ==="
echo "Server: admin.dakoiims.com"
echo "Target Directory: admin.dakoiims.com/daccounts/"
echo ""

# Step 1: Database Migration
echo "Step 1: Database Migration"
echo "=========================="
echo "1. Upload daccounts_db_export.sql to the server"
echo "2. Connect to server: ssh <EMAIL>"
echo "3. Import database:"
echo "   mysql -u dakoiim1_dakoiim1_daccounts_admin -p dakoiim1_daccounts_db < daccounts_db_export.sql"
echo "   Password: dakoiianzii"
echo ""

# Step 2: Code Deployment
echo "Step 2: Code Deployment"
echo "======================="
echo "1. Navigate to target directory:"
echo "   cd admin.dakoiims.com/daccounts/"
echo ""
echo "2. Clone GitHub repository:"
echo "   git clone https://github.com/anziinols/daccounts.git ."
echo "   Username: anziinols"
echo "   Password: *********************************************************************************************"
echo ""
echo "3. Set file permissions:"
echo "   chmod -R 755 ."
echo "   chmod -R 777 writable/"
echo ""

# Step 3: Configuration
echo "Step 3: Configuration"
echo "===================="
echo "1. Update database configuration in app/Config/Database.php:"
echo "   hostname: localhost"
echo "   username: dakoiim1_dakoiim1_daccounts_admin"
echo "   password: dakoiianzii"
echo "   database: dakoiim1_daccounts_db"
echo ""

# Step 4: Verification
echo "Step 4: Verification"
echo "==================="
echo "1. Test the application in browser"
echo "2. Verify database connection"
echo "3. Check all CRUD operations"
echo ""

echo "=== Deployment Complete ==="
echo "Application URL: https://admin.dakoiims.com/daccounts/"

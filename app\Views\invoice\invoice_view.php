<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title"><?= ucfirst($invoice['invoice_type']) ?> #<?= esc($invoice['invoice_number']) ?></h1>
        <p class="page-subtitle">
            <span class="status-badge status-<?= $invoice['status'] ?>">
                <?= ucfirst($invoice['status']) ?>
            </span>
            • Created on <?= date('M d, Y', strtotime($invoice['created_at'])) ?>
        </p>
    </div>
    <div class="page-actions">
        <?php if ($invoice['status'] !== 'paid'): ?>
            <a href="<?= base_url('invoices/' . $invoice['id'] . '/edit') ?>" class="btn btn-outline-primary">
                <i class="fas fa-edit"></i>
                Edit
            </a>
        <?php endif; ?>
        
        <?php if ($invoice['invoice_type'] === 'quotation' && in_array($invoice['status'], ['sent', 'viewed', 'accepted'])): ?>
            <a href="<?= base_url('invoices/' . $invoice['id'] . '/convert') ?>" class="btn btn-success" onclick="return confirm('Convert this quotation to an invoice?')">
                <i class="fas fa-exchange-alt"></i>
                Convert to Invoice
            </a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <!-- Main Content -->
    <div class="col-md-8">
        <!-- Invoice Details -->
        <div class="admin-card mb-4">
            <div class="admin-card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>From:</h5>
                        <?php if (!empty($invoice['trading_name'])): ?>
                            <div class="d-flex align-items-start mb-2">
                                <?php if (!empty($invoice['logo'])): ?>
                                    <img src="<?= base_url($invoice['logo']) ?>" alt="Company Logo" class="me-3" style="max-height: 60px;">
                                <?php endif; ?>
                                <div>
                                    <strong><?= esc($invoice['trading_name']) ?></strong><br>
                                    <small class="text-muted"><?= esc($invoice['account_name']) ?></small>
                                </div>
                            </div>
                            <?php if (!empty($invoice['account_address'])): ?>
                                <?= nl2br(esc($invoice['account_address'])) ?><br>
                            <?php endif; ?>
                            <?php if (!empty($invoice['account_phone'])): ?>
                                Phone: <?= esc($invoice['account_phone']) ?><br>
                            <?php endif; ?>
                            <?php if (!empty($invoice['account_email'])): ?>
                                Email: <?= esc($invoice['account_email']) ?><br>
                            <?php endif; ?>
                            <?php if (!empty($invoice['account_website'])): ?>
                                Website: <a href="<?= esc($invoice['account_website']) ?>" target="_blank"><?= esc($invoice['account_website']) ?></a>
                            <?php endif; ?>
                        <?php else: ?>
                            <strong>DAKOII ACCOUNTS</strong><br>
                            <span class="text-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                No account linked to this invoice
                            </span><br>
                            <small class="text-muted">Please link an account to display proper company information</small>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <h5>To:</h5>
                        <strong><?= esc($invoice['client_name']) ?></strong><br>
                        <?php if (!empty($invoice['client_address'])): ?>
                            <?= nl2br(esc($invoice['client_address'])) ?><br>
                        <?php endif; ?>
                        <?php if (!empty($invoice['client_email'])): ?>
                            Email: <?= esc($invoice['client_email']) ?><br>
                        <?php endif; ?>
                        <?php if (!empty($invoice['client_phone'])): ?>
                            Phone: <?= esc($invoice['client_phone']) ?>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <strong><?= ucfirst($invoice['invoice_type']) ?> Number:</strong> <?= esc($invoice['invoice_number']) ?><br>
                        <strong>Issue Date:</strong> <?= date('M d, Y', strtotime($invoice['issue_date'])) ?><br>
                        <?php if ($invoice['due_date']): ?>
                            <strong>Due Date:</strong> <?= date('M d, Y', strtotime($invoice['due_date'])) ?>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <strong>Status:</strong> 
                        <span class="status-badge status-<?= $invoice['status'] ?>">
                            <?= ucfirst($invoice['status']) ?>
                        </span>
                    </div>
                </div>

                <!-- Banking Details Section -->
                <?php if (!empty($invoice['account_number']) || !empty($invoice['bank']) || !empty($invoice['swift_code'])): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="banking-details-section p-3 bg-light border rounded">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-university"></i>
                                Banking Details
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <?php if (!empty($invoice['account_name'])): ?>
                                        <div class="mb-2">
                                            <strong>Account Name:</strong> <?= esc($invoice['account_name']) ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if (!empty($invoice['account_number'])): ?>
                                        <div class="mb-2">
                                            <strong>Account Number:</strong> <?= esc($invoice['account_number']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <?php if (!empty($invoice['bank'])): ?>
                                        <div class="mb-2">
                                            <strong>Bank:</strong> <?= esc($invoice['bank']) ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if (!empty($invoice['bank_branch'])): ?>
                                        <div class="mb-2">
                                            <strong>Branch:</strong> <?= esc($invoice['bank_branch']) ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if (!empty($invoice['swift_code'])): ?>
                                        <div class="mb-2">
                                            <strong>SWIFT Code:</strong> <?= esc($invoice['swift_code']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Items Table -->
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>Description</th>
                                <th width="100">Quantity</th>
                                <th width="120">Unit Price</th>
                                <th width="120">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($invoice['items'])): ?>
                                <?php foreach ($invoice['items'] as $item): ?>
                                    <tr>
                                        <td><?= nl2br(esc($item['description'])) ?></td>
                                        <td class="text-center"><?= number_format($item['quantity'], 2) ?></td>
                                        <td class="text-end">$<?= number_format($item['unit_price'], 2) ?></td>
                                        <td class="text-end">$<?= number_format($item['line_total'], 2) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No items found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Totals -->
                <div class="row">
                    <div class="col-md-6">
                        <?php if (!empty($invoice['notes'])): ?>
                            <h6>Notes:</h6>
                            <p><?= nl2br(esc($invoice['notes'])) ?></p>
                        <?php endif; ?>
                        
                        <?php if (!empty($invoice['terms_conditions'])): ?>
                            <h6>Terms & Conditions:</h6>
                            <p><?= nl2br(esc($invoice['terms_conditions'])) ?></p>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Subtotal:</strong></td>
                                <td class="text-end">$<?= number_format($invoice['subtotal'], 2) ?></td>
                            </tr>
                            <?php if ($invoice['discount_amount'] > 0): ?>
                                <tr>
                                    <td><strong>Discount:</strong></td>
                                    <td class="text-end">-$<?= number_format($invoice['discount_amount'], 2) ?></td>
                                </tr>
                            <?php endif; ?>
                            <?php if ($invoice['tax_rate'] > 0): ?>
                                <tr>
                                    <td><strong>Tax (<?= number_format($invoice['tax_rate'], 2) ?>%):</strong></td>
                                    <td class="text-end">$<?= number_format($invoice['tax_amount'], 2) ?></td>
                                </tr>
                            <?php endif; ?>
                            <tr class="table-active">
                                <td><strong>Total:</strong></td>
                                <td class="text-end"><strong>$<?= number_format($invoice['total_amount'], 2) ?></strong></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Stamp, Signature, and QR Code Section -->
                <div class="stamp-signature-section">
                    <div class="row">
                        <div class="col-md-4">
                            <?php if (!empty($invoice['stamp'])): ?>
                                <div class="text-center">
                                    <h6 class="mb-3 text-primary">Company Stamp</h6>
                                    <img src="<?= base_url($invoice['stamp']) ?>" alt="Company Stamp" class="img-fluid" style="max-height: 100px; max-width: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px;">
                                    <?php if (!empty($invoice['trading_name'])): ?>
                                        <div class="mt-2">
                                            <small class="text-muted"><?= esc($invoice['trading_name']) ?></small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center text-muted">
                                    <h6 class="mb-3">Company Stamp</h6>
                                    <div style="height: 100px; border: 1px dashed #ccc; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                        <small>No stamp available</small>
                                    </div>
                                    <?php if (!empty($invoice['trading_name'])): ?>
                                        <div class="mt-2">
                                            <small class="text-muted"><?= esc($invoice['trading_name']) ?></small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-4">
                            <?php if (!empty($invoice['signature'])): ?>
                                <div class="text-center">
                                    <h6 class="mb-3 text-primary">Authorized Signature</h6>
                                    <img src="<?= base_url($invoice['signature']) ?>" alt="Authorized Signature" class="img-fluid" style="max-height: 80px; max-width: 200px;">
                                    <div class="signature-line"></div>
                                    <small class="text-muted">Authorized Signatory</small>
                                </div>
                            <?php else: ?>
                                <div class="text-center text-muted">
                                    <h6 class="mb-3">Authorized Signature</h6>
                                    <div style="height: 80px; border: 1px dashed #ccc; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                        <small>No signature available</small>
                                    </div>
                                    <div class="signature-line"></div>
                                    <small class="text-muted">Authorized Signatory</small>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="mb-3 text-primary">Verification QR Code</h6>
                                <div id="qr-code-container" class="d-flex justify-content-center">
                                    <?php
                                    // Generate QR code using PHP library
                                    try {
                                        // Include the QR code library
                                        require_once ROOTPATH . 'vendor/autoload.php';
                                        
                                        // Create QR code data
                                        $verifyUrl = base_url('invoices/' . $invoice['id']);
                                        $qrText = "Invoice Verification\n";
                                        $qrText .= "Invoice: " . $invoice['invoice_number'] . "\n";
                                        $qrText .= "Type: " . ucfirst($invoice['invoice_type']) . "\n";
                                        $qrText .= "Amount: $" . number_format($invoice['total_amount'], 2) . "\n";
                                        $qrText .= "Date: " . date('M d, Y', strtotime($invoice['issue_date'])) . "\n";
                                        $qrText .= "Client: " . $invoice['client_name'] . "\n";
                                        $qrText .= "Verify: " . $verifyUrl;
                                        
                                        // Generate QR code
                                        $qrCode = new \chillerlan\QRCode\QRCode();
                                        $qrImage = $qrCode->render($qrText);
                                        
                                        // Display QR code
                                        echo '<img src="' . $qrImage . '" alt="Invoice Verification QR Code" style="max-width: 120px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">';
                                    } catch (Exception $e) {
                                        // Fallback to text-based QR code if generation fails
                                        echo '<div style="width: 120px; height: 120px; border: 2px solid #007bff; display: flex; flex-direction: column; align-items: center; justify-content: center; border-radius: 8px; background-color: #f8f9fa; padding: 10px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">';
                                        echo '<div style="font-size: 10px; font-weight: bold; color: #007bff; margin-bottom: 5px;">INVOICE QR</div>';
                                        echo '<div style="font-size: 8px; color: #333; line-height: 1.2;">';
                                        echo esc($invoice['invoice_number']) . '<br>';
                                        echo '$' . number_format($invoice['total_amount'], 2) . '<br>';
                                        echo '<a href="' . $verifyUrl . '" target="_blank" style="color: #007bff; text-decoration: none; font-size: 7px;">Verify Online</a>';
                                        echo '</div>';
                                        echo '</div>';
                                    }
                                    ?>
                                </div>
                                <small class="text-muted mt-2 d-block">Scan to verify invoice authenticity</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="admin-card mb-4">
            <div class="admin-card-header">
                <h5 class="admin-card-title">Quick Actions</h5>
            </div>
            <div class="admin-card-body">
                <div class="d-grid gap-2">
                    <?php if ($invoice['status'] !== 'paid'): ?>
                        <a href="<?= base_url('invoices/' . $invoice['id'] . '/edit') ?>" class="btn btn-outline-primary">
                            <i class="fas fa-edit"></i>
                            Edit <?= ucfirst($invoice['invoice_type']) ?>
                        </a>
                    <?php endif; ?>

                    <button class="btn btn-outline-info" onclick="exportInvoicePDF(<?= $invoice['id'] ?>)">
                        <i class="fas fa-file-pdf"></i>
                        Download PDF
                    </button>
                    
                    <?php if ($invoice['invoice_type'] === 'quotation' && in_array($invoice['status'], ['sent', 'viewed', 'accepted'])): ?>
                        <a href="<?= base_url('invoices/' . $invoice['id'] . '/convert') ?>" class="btn btn-success" onclick="return confirm('Convert this quotation to an invoice?')">
                            <i class="fas fa-exchange-alt"></i>
                            Convert to Invoice
                        </a>
                    <?php endif; ?>
                    
                    <?php if ($invoice['status'] === 'draft'): ?>
                        <a href="<?= base_url('invoices/' . $invoice['id'] . '/delete') ?>" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to delete this <?= $invoice['invoice_type'] ?>?')">
                            <i class="fas fa-trash"></i>
                            Delete
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Invoice Information -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h5 class="admin-card-title">Information</h5>
            </div>
            <div class="admin-card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td><strong>Type:</strong></td>
                        <td><?= ucfirst($invoice['invoice_type']) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Number:</strong></td>
                        <td><?= esc($invoice['invoice_number']) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <span class="status-badge status-<?= $invoice['status'] ?>">
                                <?= ucfirst($invoice['status']) ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Created:</strong></td>
                        <td><?= date('M d, Y', strtotime($invoice['created_at'])) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Updated:</strong></td>
                        <td><?= date('M d, Y', strtotime($invoice['updated_at'])) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Created by:</strong></td>
                        <td><?= esc($invoice['first_name'] . ' ' . $invoice['last_name']) ?></td>
                    </tr>
                    <?php if ($invoice['converted_from_id']): ?>
                        <tr>
                            <td><strong>Converted from:</strong></td>
                            <td>
                                <a href="<?= base_url('invoices/' . $invoice['converted_from_id']) ?>">
                                    View Original Quotation
                                </a>
                            </td>
                        </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.stamp-signature-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.stamp-signature-section .col-md-4 {
    border-right: 1px solid #dee2e6;
}

.stamp-signature-section .col-md-4:last-child {
    border-right: none;
}

.signature-line {
    border-top: 2px solid #333;
    width: 180px;
    margin: 15px auto 5px;
}

.banking-details-section {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.banking-details-section:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.banking-details-section h6 {
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
    margin-bottom: 15px;
}

.banking-details-section .mb-2 {
    font-size: 14px;
    line-height: 1.4;
}

#qr-code-container {
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}


</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
/**
 * Export invoice as PDF using direct download approach
 * Following the PDF export guide implementation
 */
function exportInvoicePDF(invoiceId) {
    const button = event.target.closest('button');
    const originalContent = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating PDF...';
    button.disabled = true;

    try {
        // Create direct link for GET request (no CSRF needed)
        const exportUrl = `<?= base_url('invoices/') ?>${invoiceId}/export-pdf`;
        
        // Open in new tab for download
        window.open(exportUrl, '_blank');

        // Restore button state after a short delay
        setTimeout(() => {
            button.innerHTML = originalContent;
            button.disabled = false;
        }, 1000);

    } catch (error) {
        console.error('PDF Export Error:', error);
        alert('Failed to export PDF. Please try again.');
        
        // Restore button state
        button.innerHTML = originalContent;
        button.disabled = false;
    }
}
</script>
<?= $this->endSection() ?>

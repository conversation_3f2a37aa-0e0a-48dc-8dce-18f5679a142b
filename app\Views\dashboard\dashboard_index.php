<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><?= $title ?? 'Dashboard - Dakoii Accounts' ?></title>
    <meta name="description" content="Dakoii Accounts - Company Accounting System Dashboard">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" type="image/x-icon" href="<?= base_url('public/assets/icons/favicon.ico') ?>">
    <link rel="icon" type="image/x-icon" href="<?= base_url('public/assets/icons/favicon.ico') ?>">
    <link rel="apple-touch-icon" href="<?= base_url('public/assets/icons/favicon.ico') ?>">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #2e8b57, #32cd32);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-section img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }

        .logo-section h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info {
            text-align: right;
        }

        .user-name {
            font-weight: 600;
            font-size: 1rem;
        }

        .user-role {
            font-size: 0.85rem;
            opacity: 0.9;
            text-transform: capitalize;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }

        /* Main Content */
        .main-content {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        /* Alert Messages */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            animation: slideDown 0.3s ease-out;
        }

        .alert-success {
            background: #f0fff4;
            color: #2f855a;
            border: 1px solid #9ae6b4;
        }

        .alert-error {
            background: #fee;
            color: #c53030;
            border: 1px solid #feb2b2;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Quick Access Section */
        .quick-access-section {
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 2rem;
            background: linear-gradient(135deg, #32cd32, #2e8b57);
            border-radius: 2px;
        }

        .quick-access-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .feature-box {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-box.featured {
            background: linear-gradient(135deg, rgba(50, 205, 50, 0.05), rgba(46, 139, 87, 0.05));
            border: 2px solid #32cd32;
            box-shadow: 0 8px 25px rgba(50, 205, 50, 0.15);
        }

        .feature-box.featured::before {
            content: 'FEATURED';
            position: absolute;
            top: 1rem;
            right: -2rem;
            background: linear-gradient(135deg, #32cd32, #2e8b57);
            color: white;
            padding: 0.25rem 3rem;
            font-size: 0.75rem;
            font-weight: 600;
            transform: rotate(45deg);
            letter-spacing: 0.05em;
        }

        .feature-box:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
        }

        .feature-box.featured:hover {
            box-shadow: 0 12px 30px rgba(50, 205, 50, 0.25);
        }

        .feature-icon {
            width: 70px;
            height: 70px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #32cd32, #2e8b57);
        }

        .feature-icon.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }

        .feature-icon.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 0.75rem;
        }

        .feature-description {
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .feature-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            border: 1px solid rgba(50, 205, 50, 0.2);
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 1.75rem;
            font-weight: 700;
            color: #2e8b57;
        }

        .stat-label {
            font-size: 0.85rem;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .feature-btn {
            background: linear-gradient(135deg, #32cd32, #2e8b57);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            text-decoration: none;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            width: 100%;
        }

        .feature-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(50, 205, 50, 0.3);
            color: white;
            text-decoration: none;
        }

        .feature-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .feature-link {
            color: #4a5568;
            text-decoration: none;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            border: 1px solid #e2e8f0;
        }

        .feature-link:hover {
            background: #f7fafc;
            color: #2e8b57;
            text-decoration: none;
            border-color: #32cd32;
        }

        /* Dashboard Cards */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .card-icon.welcome {
            background: linear-gradient(135deg, #32cd32, #2e8b57);
        }

        .card-icon.stats {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
        }

        .card-icon.profile {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1a202c;
        }

        .card-content {
            color: #4a5568;
            line-height: 1.6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: #f7fafc;
            border-radius: 8px;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2e8b57;
        }

        .stat-label {
            font-size: 0.85rem;
            color: #718096;
            margin-top: 0.25rem;
        }

        /* Quick Actions */
        .quick-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1.5rem;
        }

        .action-btn {
            background: linear-gradient(135deg, #32cd32, #2e8b57);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(50, 205, 50, 0.3);
            color: white;
            text-decoration: none;
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }

        .action-btn.secondary:hover {
            box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                padding: 0 1rem;
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .main-content {
                padding: 0 1rem;
            }

            .quick-access-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .feature-box {
                padding: 1.5rem;
            }

            .feature-stats {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <img src="<?= base_url('public/assets/images/logo.png') ?>" alt="Dakoii Accounts Logo">
                <h1>DAKOII ACCOUNTS</h1>
            </div>
            <div class="user-section">
                <div class="user-info">
                    <div class="user-name"><?= esc($user['first_name'] . ' ' . $user['last_name']) ?></div>
                    <div class="user-role"><?= esc($user['role']) ?></div>
                </div>
                <a href="<?= base_url('auth/logout') ?>" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Flash Messages -->
        <?php if (session()->getFlashdata('success')): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?= session()->getFlashdata('success') ?>
            </div>
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i>
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <!-- Quick Access Section -->
        <div class="quick-access-section">
            <h2 class="section-title">Quick Access</h2>
            <div class="quick-access-grid">
                <!-- Invoice Management Feature Box -->
                <div class="feature-box featured">
                    <div class="feature-icon">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                    <div class="feature-content">
                        <h3 class="feature-title">Invoice Management</h3>
                        <p class="feature-description">Create, manage, and track invoices and quotations. Generate professional documents and monitor payment status.</p>
                        <div class="feature-stats">
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Draft Invoices</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Pending Quotes</span>
                            </div>
                        </div>
                        <a href="<?= base_url('invoices') ?>" class="feature-btn">
                            <i class="fas fa-arrow-right"></i>
                            Access Invoice Management
                        </a>
                    </div>
                </div>

                <!-- Account Management -->
                <div class="feature-box">
                    <div class="feature-icon success">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="feature-content">
                        <h3 class="feature-title">Account Management</h3>
                        <p class="feature-description">Manage organizational accounts for invoice branding and banking information.</p>
                        <div class="feature-stats">
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Active Accounts</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Default Set</span>
                            </div>
                        </div>
                        <a href="<?= base_url('accounts') ?>" class="feature-btn">
                            <i class="fas fa-arrow-right"></i>
                            Manage Accounts
                        </a>
                    </div>
                </div>

                <!-- Profile Management -->
                <div class="feature-box">
                    <div class="feature-icon secondary">
                        <i class="fas fa-user-cog"></i>
                    </div>
                    <div class="feature-content">
                        <h3 class="feature-title">Profile Settings</h3>
                        <p class="feature-description">Manage your account information and security settings.</p>
                        <div class="feature-actions">
                            <a href="<?= base_url('dashboard/profile') ?>" class="feature-link">
                                <i class="fas fa-user"></i>
                                Edit Profile
                            </a>
                            <a href="<?= base_url('dashboard/change-password') ?>" class="feature-link">
                                <i class="fas fa-key"></i>
                                Change Password
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Welcome Card -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon welcome">
                        <i class="fas fa-home"></i>
                    </div>
                    <h2 class="card-title">Welcome Back!</h2>
                </div>
                <div class="card-content">
                    <p>Hello <strong><?= esc($user['first_name']) ?></strong>, welcome to your Dakoii Accounts dashboard. You can manage your accounting system from here.</p>
                </div>
            </div>

            <!-- Statistics Card -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon stats">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h2 class="card-title">System Statistics</h2>
                </div>
                <div class="card-content">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number"><?= $stats['total_users'] ?></div>
                            <div class="stat-label">Active Users</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?= ucfirst($stats['user_role']) ?></div>
                            <div class="stat-label">Your Role</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Card -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon profile">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <h2 class="card-title">Your Profile</h2>
                </div>
                <div class="card-content">
                    <p><strong>Email:</strong> <?= esc($user['email']) ?></p>
                    <p><strong>Role:</strong> <?= ucfirst(esc($user['role'])) ?></p>
                    <p><strong>Status:</strong> <?= ucfirst(esc($user['status'])) ?></p>
                    <div class="quick-actions">
                        <?php if ($stats['user_role'] === 'admin'): ?>
                            <a href="<?= base_url('dashboard/users') ?>" class="action-btn">
                                <i class="fas fa-users"></i>
                                Manage Users
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>

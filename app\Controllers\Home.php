<?php

namespace App\Controllers;

class Home extends BaseController
{
    public function index()
    {
        // Check if user is logged in
        $session = session();

        if ($session->get('isLoggedIn')) {
            // User is logged in, redirect to dashboard
            return redirect()->to('/dashboard');
        } else {
            // User is not logged in, redirect to login page
            return redirect()->to('/auth/login');
        }
    }
}

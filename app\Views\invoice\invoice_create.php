<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="page-header">
    <h1 class="page-title">Create <?= ucfirst($invoice['invoice_type']) ?></h1>
    <p class="page-subtitle">Fill in the details to create a new <?= $invoice['invoice_type'] ?></p>
</div>

<!-- Invoice Form -->
<form method="POST" action="<?= base_url('invoices') ?>" id="invoiceForm">
    <?= csrf_field() ?>
    
    <div class="row">
        <!-- Left Column -->
        <div class="col-md-8">
            <!-- Basic Information -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Basic Information</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="invoice_type" class="form-label">Type <span class="text-danger">*</span></label>
                                <select name="invoice_type" id="invoice_type" class="form-select" required>
                                    <option value="invoice" <?= $invoice['invoice_type'] === 'invoice' ? 'selected' : '' ?>>Invoice</option>
                                    <option value="quotation" <?= $invoice['invoice_type'] === 'quotation' ? 'selected' : '' ?>>Quotation</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="client_id" class="form-label">Client <span class="text-danger">*</span></label>
                                <select name="client_id" id="client_id" class="form-select" required>
                                    <option value="">Select Client</option>
                                    <?php foreach ($clients as $clientId => $clientName): ?>
                                        <option value="<?= $clientId ?>"><?= esc($clientName) ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">
                                    <a href="<?= base_url('clients/create') ?>" target="_blank">Create new client</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="account_id" class="form-label">Account</label>
                                <select name="account_id" id="account_id" class="form-select">
                                    <option value="">Use Default Account</option>
                                    <?php foreach ($accounts as $accountId => $accountName): ?>
                                        <option value="<?= $accountId ?>"><?= esc($accountName) ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <small class="text-muted">Select which organizational account to use for this invoice</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Empty column for spacing -->
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="issue_date" class="form-label">Issue Date <span class="text-danger">*</span></label>
                                <input type="date" name="issue_date" id="issue_date" class="form-control" value="<?= $invoice['issue_date'] ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="due_date" class="form-label">Due Date</label>
                                <input type="date" name="due_date" id="due_date" class="form-control" value="<?= $invoice['due_date'] ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Items -->
            <div class="admin-card mb-4">
                <div class="admin-card-header d-flex justify-content-between align-items-center">
                    <h5 class="admin-card-title">Items</h5>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-sm btn-outline-info" onclick="openAIAssistant()">
                            <i class="fas fa-robot"></i> AI Assistant
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addItem()">
                            <i class="fas fa-plus"></i> Add Item
                        </button>
                    </div>
                </div>
                <div class="admin-card-body">
                    <div id="items-container">
                        <?php foreach ($items as $index => $item): ?>
                            <div class="item-row mb-3" data-index="<?= $index ?>">
                                <div class="row align-items-end">
                                    <div class="col-md-5">
                                        <label class="form-label">Description <span class="text-danger">*</span></label>
                                        <textarea name="item_description[]" class="form-control" rows="2" placeholder="Item description" required><?= esc($item['description']) ?></textarea>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">Quantity <span class="text-danger">*</span></label>
                                        <input type="number" name="item_quantity[]" class="form-control quantity-input" step="0.01" min="0.01" value="<?= $item['quantity'] ?>" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">Unit Price <span class="text-danger">*</span></label>
                                        <input type="number" name="item_unit_price[]" class="form-control price-input" step="0.01" min="0" value="<?= $item['unit_price'] ?>" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">Total</label>
                                        <input type="text" class="form-control line-total" readonly value="$0.00">
                                    </div>
                                    <div class="col-md-1">
                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeItem(this)" <?= $index === 0 ? 'style="visibility: hidden;"' : '' ?>>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Notes and Terms -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Additional Information</h5>
                </div>
                <div class="admin-card-body">
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="Internal notes (not visible to client)"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="terms_conditions" class="form-label">Terms & Conditions</label>
                        <textarea name="terms_conditions" id="terms_conditions" class="form-control" rows="3" placeholder="Terms and conditions for this invoice"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-md-4">
            <!-- Summary -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Summary</h5>
                </div>
                <div class="admin-card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span id="subtotal">$0.00</span>
                    </div>
                    
                    <div class="mb-3">
                        <label for="discount_amount" class="form-label">Discount Amount</label>
                        <input type="number" name="discount_amount" id="discount_amount" class="form-control" step="0.01" min="0" value="<?= $invoice['discount_amount'] ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="tax_rate" class="form-label">Tax Rate (%)</label>
                        <input type="number" name="tax_rate" id="tax_rate" class="form-control" step="0.01" min="0" max="100" value="<?= $invoice['tax_rate'] ?>">
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tax Amount:</span>
                        <span id="tax_amount">$0.00</span>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <strong>Total:</strong>
                        <strong id="total_amount">$0.00</strong>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-select">
                            <option value="draft" <?= $invoice['status'] === 'draft' ? 'selected' : '' ?>>Draft</option>
                            <option value="sent" <?= $invoice['status'] === 'sent' ? 'selected' : '' ?>>Sent</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="admin-card">
                <div class="admin-card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Create <?= ucfirst($invoice['invoice_type']) ?>
                        </button>
                        <a href="<?= base_url('invoices') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- AI Assistant Modal -->
<div class="modal fade ai-assistant-modal" id="aiAssistantModal" tabindex="-1" aria-labelledby="aiAssistantModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="aiAssistantModalLabel">
                    <i class="fas fa-robot text-info"></i> AI Assistant
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="<?= base_url('invoices/generate-ai-data') ?>">
                <?= csrf_field() ?>
                <input type="hidden" name="invoice_type" id="ai_invoice_type" value="<?= $invoice['invoice_type'] ?>">
                <input type="hidden" name="return_url" value="<?= current_url() ?>">

                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Describe your invoice/quotation requirements and the AI will generate the form data for you.
                    </div>

                    <div class="mb-3">
                        <label for="aiPrompt" class="form-label">Describe your invoice/quotation:</label>
                        <textarea name="prompt" id="aiPrompt" class="form-control" rows="8" placeholder="Example: Create an invoice for web development services. Client is ABC Company. Include:
- Website design: $1500
- Frontend development: $2000
- Backend development: $2500
- Domain & hosting setup: $200

Tax rate: 10%
Payment terms: Net 30 days
Add standard web development terms and conditions." required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="aiClientHint" class="form-label">Client Name (optional):</label>
                        <input type="text" name="client_hint" id="aiClientHint" class="form-control" placeholder="Enter client name to help match with existing clients">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-magic"></i> Generate Invoice Data
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
let itemIndex = <?= count($items) ?>;

function addItem() {
    const container = document.getElementById('items-container');
    const itemHtml = `
        <div class="item-row mb-3" data-index="${itemIndex}">
            <div class="row align-items-end">
                <div class="col-md-5">
                    <label class="form-label">Description <span class="text-danger">*</span></label>
                    <textarea name="item_description[]" class="form-control" rows="2" placeholder="Item description" required></textarea>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Quantity <span class="text-danger">*</span></label>
                    <input type="number" name="item_quantity[]" class="form-control quantity-input" step="0.01" min="0.01" value="1" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Unit Price <span class="text-danger">*</span></label>
                    <input type="number" name="item_unit_price[]" class="form-control price-input" step="0.01" min="0" value="0" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Total</label>
                    <input type="text" class="form-control line-total" readonly value="$0.00">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeItem(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', itemHtml);
    itemIndex++;
    
    // Add event listeners to new inputs
    attachEventListeners();
    calculateTotals();
}

function removeItem(button) {
    const itemRow = button.closest('.item-row');
    itemRow.remove();
    calculateTotals();
}

function attachEventListeners() {
    // Remove existing listeners to avoid duplicates
    document.querySelectorAll('.quantity-input, .price-input, #discount_amount, #tax_rate').forEach(input => {
        input.removeEventListener('input', calculateTotals);
        input.addEventListener('input', calculateTotals);
    });
}

function calculateTotals() {
    let subtotal = 0;
    
    // Calculate line totals and subtotal
    document.querySelectorAll('.item-row').forEach(row => {
        const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
        const unitPrice = parseFloat(row.querySelector('.price-input').value) || 0;
        const lineTotal = quantity * unitPrice;
        
        row.querySelector('.line-total').value = '$' + lineTotal.toFixed(2);
        subtotal += lineTotal;
    });
    
    // Get discount and tax
    const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
    const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
    
    // Calculate tax amount
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = taxableAmount * (taxRate / 100);
    
    // Calculate total
    const total = subtotal + taxAmount - discountAmount;
    
    // Update display
    document.getElementById('subtotal').textContent = '$' + subtotal.toFixed(2);
    document.getElementById('tax_amount').textContent = '$' + taxAmount.toFixed(2);
    document.getElementById('total_amount').textContent = '$' + total.toFixed(2);
}

// Initialize event listeners and calculations
document.addEventListener('DOMContentLoaded', function() {
    attachEventListeners();
    calculateTotals();
    
    // Update form title when type changes
    document.getElementById('invoice_type').addEventListener('change', function() {
        const type = this.value;
        document.querySelector('.page-title').textContent = 'Create ' + type.charAt(0).toUpperCase() + type.slice(1);
        document.querySelector('.page-subtitle').textContent = 'Fill in the details to create a new ' + type;
        document.querySelector('button[type="submit"]').innerHTML = '<i class="fas fa-save"></i> Create ' + type.charAt(0).toUpperCase() + type.slice(1);
    });

    // Update AI form invoice type when main form type changes
    document.getElementById('invoice_type').addEventListener('change', function() {
        document.getElementById('ai_invoice_type').value = this.value;
    });

    // Check if we have AI-generated data and refresh CSRF token if needed
    <?php if (session()->getFlashdata('ai_generated_data')): ?>
    // Small delay to ensure form is fully loaded before refreshing CSRF token
    setTimeout(() => {
        refreshCSRFToken();
    }, 500);
    <?php endif; ?>

});

// Function to refresh CSRF token after AI Assistant usage
function refreshCSRFToken() {
    console.log('Refreshing CSRF token after AI Assistant usage...');

    // Log current CSRF token for debugging
    const currentCsrfInput = document.querySelector('input[name="<?= csrf_token() ?>"]');
    if (currentCsrfInput) {
        console.log('Current CSRF token:', currentCsrfInput.value.substring(0, 10) + '...');
    }

    // Make a request to get a fresh CSRF token
    fetch('<?= base_url('invoices/refresh-csrf') ?>', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.csrf_token && data.csrf_name) {
            // Update the CSRF token in the current form
            const currentCsrfInput = document.querySelector('input[name="' + data.csrf_name + '"]');
            if (currentCsrfInput) {
                const oldToken = currentCsrfInput.value.substring(0, 10) + '...';
                currentCsrfInput.value = data.csrf_token;
                const newToken = data.csrf_token.substring(0, 10) + '...';
                console.log('✅ CSRF token refreshed successfully after AI Assistant');
                console.log('Old token:', oldToken, '→ New token:', newToken);

                // Show a brief success message
                showBriefMessage('Security token refreshed - form ready for submission', 'success');
            } else {
                console.error('CSRF input field not found');
                showBriefMessage('Warning: Security token update failed', 'warning');
            }
        } else {
            console.error('Failed to get fresh CSRF token from response');
            showBriefMessage('Warning: Security token update failed', 'warning');
        }
    })
    .catch(error => {
        console.error('Failed to refresh CSRF token:', error);
        showBriefMessage('Warning: Security token update failed', 'warning');
    });
}

// Function to show brief status messages
function showBriefMessage(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', alertHtml);

    // Auto-hide after 3 seconds
    setTimeout(() => {
        const alert = document.querySelector('.alert:last-of-type');
        if (alert) {
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 300);
        }
    }, 3000);
}

// AI Assistant Functions
function openAIAssistant() {
    const modal = new bootstrap.Modal(document.getElementById('aiAssistantModal'));
    modal.show();
}
</script>
<?= $this->endSection() ?>

<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    /* Dashboard-specific styles */
    .dashboard-main {
        padding: 2rem;
    }

    /* Quick Access Section */
    .quick-access-section {
        margin-bottom: 3rem;
    }

    .section-title {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1a202c;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title::before {
        content: '';
        width: 4px;
        height: 2rem;
        background: linear-gradient(135deg, #32cd32, #2e8b57);
        border-radius: 2px;
    }

    .quick-access-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .feature-box {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .feature-box.featured {
        background: linear-gradient(135deg, rgba(50, 205, 50, 0.05), rgba(46, 139, 87, 0.05));
        border: 2px solid #32cd32;
        box-shadow: 0 8px 25px rgba(50, 205, 50, 0.15);
    }

    .feature-box.featured::before {
        content: 'FEATURED';
        position: absolute;
        top: 1rem;
        right: -2rem;
        background: linear-gradient(135deg, #32cd32, #2e8b57);
        color: white;
        padding: 0.25rem 3rem;
        font-size: 0.75rem;
        font-weight: 600;
        transform: rotate(45deg);
        letter-spacing: 0.05em;
    }

    .feature-box:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
    }

    .feature-box.featured:hover {
        box-shadow: 0 12px 30px rgba(50, 205, 50, 0.25);
    }

    .feature-icon {
        width: 70px;
        height: 70px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, #32cd32, #2e8b57);
    }

    .feature-icon.secondary {
        background: linear-gradient(135deg, #6b7280, #4b5563);
    }

    .feature-icon.success {
        background: linear-gradient(135deg, #10b981, #059669);
    }

    .feature-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1a202c;
        margin-bottom: 0.75rem;
    }

    .feature-description {
        color: #4a5568;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .feature-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 2rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 12px;
        border: 1px solid rgba(50, 205, 50, 0.2);
    }

    .stat {
        text-align: center;
    }

    .feature-btn {
        background: linear-gradient(135deg, #32cd32, #2e8b57);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 12px;
        text-decoration: none;
        font-size: 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        width: 100%;
    }

    .feature-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(50, 205, 50, 0.3);
        color: white;
        text-decoration: none;
    }

    .feature-actions {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .feature-link {
        color: #4a5568;
        text-decoration: none;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        border: 1px solid #e2e8f0;
    }

    .feature-link:hover {
        background: #f7fafc;
        color: #2e8b57;
        text-decoration: none;
        border-color: #32cd32;
    }

    /* Dashboard Cards */
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .dashboard-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid #e2e8f0;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .card-icon {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }

    .card-icon.welcome {
        background: linear-gradient(135deg, #32cd32, #2e8b57);
    }

    .card-icon.stats {
        background: linear-gradient(135deg, #3b82f6, #1e40af);
    }

    .card-icon.profile {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    }

    .card-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1a202c;
    }

    .card-content {
        color: #4a5568;
        line-height: 1.6;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .stat-item {
        text-align: center;
        padding: 1rem;
        background: #f7fafc;
        border-radius: 8px;
    }

    /* Quick Actions */
    .quick-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin-top: 1.5rem;
    }

    .action-btn {
        background: linear-gradient(135deg, #32cd32, #2e8b57);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(50, 205, 50, 0.3);
        color: white;
        text-decoration: none;
    }

    .action-btn.secondary {
        background: linear-gradient(135deg, #6b7280, #4b5563);
    }

    .action-btn.secondary:hover {
        box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-main {
            padding: 1rem;
        }

        .quick-access-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .feature-box {
            padding: 1.5rem;
        }

        .feature-stats {
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .dashboard-grid {
            grid-template-columns: 1fr;
        }

        .quick-actions {
            justify-content: center;
        }
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Dashboard content will be inserted here -->
<?= $this->renderSection('dashboard_content') ?>
<?= $this->endSection() ?>

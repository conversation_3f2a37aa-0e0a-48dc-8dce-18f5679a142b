<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="page-header">
    <h1 class="page-title">Create New Account</h1>
    <p class="page-subtitle">Add a new organizational account for invoice and quotation generation</p>
</div>

<!-- Account Form -->
<form method="POST" action="<?= base_url('accounts') ?>" enctype="multipart/form-data" id="accountForm">
    <?= csrf_field() ?>
    
    <div class="row">
        <!-- Left Column -->
        <div class="col-md-8">
            <!-- Basic Information -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Basic Information</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="trading_name" class="form-label">Trading Name <span class="text-danger">*</span></label>
                                <input type="text" name="trading_name" id="trading_name" class="form-control" 
                                       value="<?= old('trading_name') ?>" required>
                                <?php if ($validation->hasError('trading_name')): ?>
                                    <div class="text-danger small"><?= $validation->getError('trading_name') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="account_name" class="form-label">Account Name <span class="text-danger">*</span></label>
                                <input type="text" name="account_name" id="account_name" class="form-control" 
                                       value="<?= old('account_name') ?>" required>
                                <?php if ($validation->hasError('account_name')): ?>
                                    <div class="text-danger small"><?= $validation->getError('account_name') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">Business Address</label>
                        <textarea name="address" id="address" class="form-control" rows="3" 
                                  placeholder="Complete business address"><?= old('address') ?></textarea>
                        <?php if ($validation->hasError('address')): ?>
                            <div class="text-danger small"><?= $validation->getError('address') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Banking Information -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Banking Information</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="account_number" class="form-label">Account Number <span class="text-danger">*</span></label>
                                <input type="text" name="account_number" id="account_number" class="form-control" 
                                       value="<?= old('account_number') ?>" required>
                                <?php if ($validation->hasError('account_number')): ?>
                                    <div class="text-danger small"><?= $validation->getError('account_number') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank" class="form-label">Bank Name <span class="text-danger">*</span></label>
                                <input type="text" name="bank" id="bank" class="form-control" 
                                       value="<?= old('bank') ?>" required>
                                <?php if ($validation->hasError('bank')): ?>
                                    <div class="text-danger small"><?= $validation->getError('bank') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank_branch" class="form-label">Bank Branch</label>
                                <input type="text" name="bank_branch" id="bank_branch" class="form-control" 
                                       value="<?= old('bank_branch') ?>">
                                <?php if ($validation->hasError('bank_branch')): ?>
                                    <div class="text-danger small"><?= $validation->getError('bank_branch') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="swift_code" class="form-label">SWIFT Code</label>
                                <input type="text" name="swift_code" id="swift_code" class="form-control" 
                                       value="<?= old('swift_code') ?>">
                                <?php if ($validation->hasError('swift_code')): ?>
                                    <div class="text-danger small"><?= $validation->getError('swift_code') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Contact Information</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="text" name="phone" id="phone" class="form-control" 
                                       value="<?= old('phone') ?>">
                                <?php if ($validation->hasError('phone')): ?>
                                    <div class="text-danger small"><?= $validation->getError('phone') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" name="email" id="email" class="form-control" 
                                       value="<?= old('email') ?>">
                                <?php if ($validation->hasError('email')): ?>
                                    <div class="text-danger small"><?= $validation->getError('email') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="website" class="form-label">Website URL</label>
                        <input type="url" name="website" id="website" class="form-control" 
                               value="<?= old('website') ?>" placeholder="https://example.com">
                        <?php if ($validation->hasError('website')): ?>
                            <div class="text-danger small"><?= $validation->getError('website') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Legal Information -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Legal Information</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tax_number" class="form-label">Tax Number</label>
                                <input type="text" name="tax_number" id="tax_number" class="form-control" 
                                       value="<?= old('tax_number') ?>">
                                <?php if ($validation->hasError('tax_number')): ?>
                                    <div class="text-danger small"><?= $validation->getError('tax_number') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="registration_number" class="form-label">Registration Number</label>
                                <input type="text" name="registration_number" id="registration_number" class="form-control" 
                                       value="<?= old('registration_number') ?>">
                                <?php if ($validation->hasError('registration_number')): ?>
                                    <div class="text-danger small"><?= $validation->getError('registration_number') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-md-4">
            <!-- File Uploads -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Branding Files</h5>
                </div>
                <div class="admin-card-body">
                    <div class="mb-3">
                        <label for="logo" class="form-label">Company Logo</label>
                        <input type="file" name="logo" id="logo" class="form-control" accept="image/*">
                        <small class="text-muted">Accepted formats: JPG, PNG, GIF. Max size: 2MB</small>
                        <?php if ($validation->hasError('logo')): ?>
                            <div class="text-danger small"><?= $validation->getError('logo') ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="stamp" class="form-label">Company Stamp</label>
                        <input type="file" name="stamp" id="stamp" class="form-control" accept="image/*">
                        <small class="text-muted">Accepted formats: JPG, PNG, GIF. Max size: 2MB</small>
                        <?php if ($validation->hasError('stamp')): ?>
                            <div class="text-danger small"><?= $validation->getError('stamp') ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="signature" class="form-label">Authorized Signature</label>
                        <input type="file" name="signature" id="signature" class="form-control" accept="image/*">
                        <small class="text-muted">Accepted formats: JPG, PNG, GIF. Max size: 2MB</small>
                        <?php if ($validation->hasError('signature')): ?>
                            <div class="text-danger small"><?= $validation->getError('signature') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Settings -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Account Settings</h5>
                </div>
                <div class="admin-card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-select" required>
                            <option value="active" <?= old('status') === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= old('status') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                        <?php if ($validation->hasError('status')): ?>
                            <div class="text-danger small"><?= $validation->getError('status') ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="form-check">
                        <!-- Hidden field to ensure is_default is always sent -->
                        <input type="hidden" name="is_default" value="0">
                        <input type="checkbox" name="is_default" id="is_default" class="form-check-input" value="1" 
                               <?= old('is_default') ? 'checked' : '' ?>>
                        <label for="is_default" class="form-check-label">
                            Set as Default Account
                        </label>
                        <small class="form-text text-muted d-block">
                            Default account will be automatically selected for new invoices
                        </small>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="admin-card">
                <div class="admin-card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Create Account
                        </button>
                        <a href="<?= base_url('accounts') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// File size validation
document.querySelectorAll('input[type="file"]').forEach(function(input) {
    input.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file && file.size > 2 * 1024 * 1024) { // 2MB limit
            alert('File size must be less than 2MB');
            this.value = '';
        }
    });
});

// Form validation
document.getElementById('accountForm').addEventListener('submit', function(e) {
    const requiredFields = ['trading_name', 'account_name', 'account_number', 'bank'];
    let isValid = true;
    
    requiredFields.forEach(function(fieldName) {
        const field = document.getElementById(fieldName);
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('Please fill in all required fields.');
    }
});

// Remove validation errors on input
document.querySelectorAll('input, select, textarea').forEach(function(element) {
    element.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });
});
</script>
<?= $this->endSection() ?>

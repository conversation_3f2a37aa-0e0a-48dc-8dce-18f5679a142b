<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class AccountSeeder extends Seeder
{
    public function run()
    {
        $data = [
            [
                'trading_name' => 'DAKOII ACCOUNTS',
                'account_name' => 'DAKOII ACCOUNTS MAIN',
                'account_number' => '**********',
                'bank' => 'Sample Bank',
                'bank_branch' => 'Main Branch',
                'swift_code' => 'SAMPLEXX',
                'address' => "123 Business Street\nBusiness District\nCity, State 12345",
                'phone' => '+****************',
                'email' => '<EMAIL>',
                'website' => 'https://dakoiims.com',
                'tax_number' => 'TAX123456789',
                'registration_number' => 'REG987654321',
                'is_default' => true,
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        // Insert the data
        $this->db->table('accounts')->insertBatch($data);
    }
}

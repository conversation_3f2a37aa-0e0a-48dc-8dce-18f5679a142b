<?php

namespace App\Models;

use CodeIgniter\Model;

class AccountModel extends Model
{
    protected $table            = 'accounts';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'trading_name',
        'account_name',
        'account_number',
        'bank',
        'bank_branch',
        'swift_code',
        'logo',
        'stamp',
        'signature',
        'address',
        'phone',
        'email',
        'website',
        'tax_number',
        'registration_number',
        'is_default',
        'status'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Soft delete fields
    protected $deletedByField = 'deleted_by';
    protected $isDeletedField = 'is_deleted';

    // Validation
    protected $validationRules      = [
        'trading_name'   => 'required|max_length[255]',
        'account_name'   => 'required|max_length[255]',
        'account_number' => 'required|max_length[100]',
        'bank'          => 'required|max_length[255]',
        'bank_branch'   => 'permit_empty|max_length[255]',
        'swift_code'    => 'permit_empty|max_length[20]',
        'logo'          => 'permit_empty|max_length[500]',
        'stamp'         => 'permit_empty|max_length[500]',
        'signature'     => 'permit_empty|max_length[500]',
        'address'       => 'permit_empty',
        'phone'         => 'permit_empty|max_length[50]',
        'email'         => 'permit_empty|valid_email|max_length[255]',
        'website'       => 'permit_empty|valid_url|max_length[255]',
        'tax_number'    => 'permit_empty|max_length[100]',
        'registration_number' => 'permit_empty|max_length[100]',
        'is_default'    => 'permit_empty|in_list[0,1,false,true]',
        'status'        => 'required|in_list[active,inactive]'
    ];

    protected $validationMessages   = [
        'trading_name' => [
            'required' => 'Trading name is required.',
            'max_length' => 'Trading name cannot exceed 255 characters.'
        ],
        'account_name' => [
            'required' => 'Account name is required.',
            'max_length' => 'Account name cannot exceed 255 characters.'
        ],
        'account_number' => [
            'required' => 'Account number is required.',
            'max_length' => 'Account number cannot exceed 100 characters.'
        ],
        'bank' => [
            'required' => 'Bank name is required.',
            'max_length' => 'Bank name cannot exceed 255 characters.'
        ],
        'email' => [
            'valid_email' => 'Please enter a valid email address.'
        ],
        'website' => [
            'valid_url' => 'Please enter a valid website URL.'
        ],
        'status' => [
            'required' => 'Status is required.',
            'in_list' => 'Status must be either active or inactive.'
        ]
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['ensureOnlyOneDefault'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = ['ensureOnlyOneDefault'];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = ['setSoftDeleteFields', 'checkInvoiceReferences'];
    protected $afterDelete    = [];

    /**
     * Ensure only one account can be marked as default
     */
    protected function ensureOnlyOneDefault(array $data)
    {
        // Handle both setting and unsetting default account
        if (isset($data['data']['is_default'])) {
            if ($data['data']['is_default'] == 1) {
                // Remove default flag from all other accounts when setting new default
                $this->where('is_deleted', false)
                     ->set('is_default', false)
                     ->update();
            } elseif ($data['data']['is_default'] == 0) {
                // Explicitly convert '0' string to boolean false
                $data['data']['is_default'] = false;
            }
        }
        
        return $data;
    }

    /**
     * Set soft delete fields before delete
     */
    protected function setSoftDeleteFields(array $data)
    {
        $data['data'][$this->deletedByField] = session()->get('user_id');
        $data['data'][$this->isDeletedField] = true;
        
        return $data;
    }

    /**
     * Check if account has invoice references before deletion
     */
    protected function checkInvoiceReferences(array $data)
    {
        $accountId = $data['id'][0] ?? null;
        
        if ($accountId) {
            $invoiceModel = new \App\Models\InvoiceModel();
            $hasInvoices = $invoiceModel->where('account_id', $accountId)
                                      ->where('is_deleted', false)
                                      ->countAllResults() > 0;
            
            if ($hasInvoices) {
                throw new \RuntimeException('Cannot delete account that is linked to existing invoices. Please reassign invoices to another account first.');
            }
        }
        
        return $data;
    }

    /**
     * Get default account
     */
    public function getDefaultAccount(): array|null
    {
        return $this->where('is_default', true)
                   ->where('status', 'active')
                   ->where('is_deleted', false)
                   ->first();
    }

    /**
     * Get active accounts for dropdown
     */
    public function getActiveAccountsForDropdown(): array
    {
        $accounts = $this->select('id, trading_name, account_name')
                        ->where('status', 'active')
                        ->where('is_deleted', false)
                        ->orderBy('trading_name', 'ASC')
                        ->findAll();
        
        $dropdown = [];
        foreach ($accounts as $account) {
            $dropdown[$account['id']] = $account['trading_name'] . ' (' . $account['account_name'] . ')';
        }
        
        return $dropdown;
    }

    /**
     * Set account as default
     */
    public function setAsDefault(int $accountId): bool
    {
        // First, remove default from all accounts
        $this->where('is_deleted', false)
             ->set('is_default', false)
             ->update();
        
        // Then set the specified account as default
        return $this->update($accountId, ['is_default' => true]);
    }

    /**
     * Get account with invoice statistics
     */
    public function getAccountWithStats(int $accountId): array|null
    {
        $account = $this->find($accountId);
        
        if (!$account) {
            return null;
        }

        // Get invoice statistics
        $invoiceModel = new \App\Models\InvoiceModel();
        $stats = $invoiceModel->select('
                COUNT(*) as total_invoices,
                SUM(CASE WHEN status = "paid" THEN total_amount ELSE 0 END) as total_paid,
                SUM(CASE WHEN status IN ("sent", "viewed") THEN total_amount ELSE 0 END) as total_outstanding
            ')
            ->where('account_id', $accountId)
            ->where('is_deleted', false)
            ->first();

        $account['stats'] = $stats ?: [
            'total_invoices' => 0,
            'total_paid' => 0.00,
            'total_outstanding' => 0.00
        ];
        
        return $account;
    }

    /**
     * Check if account has any invoices
     */
    public function hasInvoices(int $accountId): bool
    {
        $invoiceModel = new \App\Models\InvoiceModel();
        return $invoiceModel->where('account_id', $accountId)
                           ->where('is_deleted', false)
                           ->countAllResults() > 0;
    }

    /**
     * Get accounts with pagination and search
     */
    public function getAccountsPaginated(int $perPage = 10, array $filters = []): array
    {
        $builder = $this->where('is_deleted', false);
        
        // Apply filters
        if (!empty($filters['search'])) {
            $builder->groupStart()
                   ->like('trading_name', $filters['search'])
                   ->orLike('account_name', $filters['search'])
                   ->orLike('bank', $filters['search'])
                   ->groupEnd();
        }
        
        if (!empty($filters['status'])) {
            $builder->where('status', $filters['status']);
        }
        
        return [
            'accounts' => $builder->orderBy('trading_name', 'ASC')->paginate($perPage),
            'pager' => $this->pager
        ];
    }
}

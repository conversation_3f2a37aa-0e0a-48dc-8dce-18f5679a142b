<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header">
    <h1 class="page-title">Edit Client</h1>
    <p class="page-subtitle">Update client information</p>
</div>

<!-- Client Form -->
<div class="admin-card">
    <div class="admin-card-header">
        <h5 class="admin-card-title">Client Information</h5>
    </div>
    <div class="admin-card-body">
        <?php if (session()->getFlashdata('error')): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <?php if (session()->getFlashdata('validation')): ?>
            <div class="alert alert-danger">
                <h6>Please fix the following errors:</h6>
                <ul class="mb-0">
                    <?php foreach (session()->getFlashdata('validation') as $error): ?>
                        <li><?= esc($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <form method="POST" action="<?= base_url('clients/' . $client['id']) ?>">
            <?= csrf_field() ?>
            <input type="hidden" name="_method" value="PUT">

            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6">
                    <h6 class="mb-3 text-primary">
                        <i class="fas fa-user"></i>
                        Basic Information
                    </h6>

                    <div class="mb-3">
                        <label for="name" class="form-label">Client Name <span class="text-danger">*</span></label>
                        <input type="text" name="name" id="name" class="form-control" 
                               value="<?= old('name', $client['name']) ?>" required maxlength="100">
                        <div class="form-text">Full name or company name of the client</div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" name="email" id="email" class="form-control" 
                               value="<?= old('email', $client['email']) ?>" maxlength="100">
                        <div class="form-text">Primary email address for communications</div>
                    </div>

                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="text" name="phone" id="phone" class="form-control" 
                               value="<?= old('phone', $client['phone']) ?>" maxlength="20">
                        <div class="form-text">Primary contact phone number</div>
                    </div>

                    <div class="mb-3">
                        <label for="tax_id" class="form-label">Tax ID / VAT Number</label>
                        <input type="text" name="tax_id" id="tax_id" class="form-control" 
                               value="<?= old('tax_id', $client['tax_id']) ?>" maxlength="50">
                        <div class="form-text">Tax identification number for invoicing</div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="col-md-6">
                    <h6 class="mb-3 text-primary">
                        <i class="fas fa-map-marker-alt"></i>
                        Address Information
                    </h6>

                    <div class="mb-3">
                        <label for="address" class="form-label">Street Address</label>
                        <textarea name="address" id="address" class="form-control" rows="3"><?= old('address', $client['address']) ?></textarea>
                        <div class="form-text">Full street address including building number and street name</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="city" class="form-label">City</label>
                                <input type="text" name="city" id="city" class="form-control" 
                                       value="<?= old('city', $client['city']) ?>" maxlength="50">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="state" class="form-label">State/Province</label>
                                <input type="text" name="state" id="state" class="form-control" 
                                       value="<?= old('state', $client['state']) ?>" maxlength="50">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="zip_code" class="form-label">ZIP/Postal Code</label>
                                <input type="text" name="zip_code" id="zip_code" class="form-control" 
                                       value="<?= old('zip_code', $client['zip_code']) ?>" maxlength="10">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="country" class="form-label">Country</label>
                                <input type="text" name="country" id="country" class="form-control" 
                                       value="<?= old('country', $client['country']) ?>" maxlength="50">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Audit Information -->
            <div class="row mt-4">
                <div class="col-12">
                    <h6 class="mb-3 text-secondary">
                        <i class="fas fa-info-circle"></i>
                        Record Information
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Created Date</label>
                                <input type="text" class="form-control" readonly 
                                       value="<?= date('F j, Y g:i A', strtotime($client['created_at'])) ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Last Updated</label>
                                <input type="text" class="form-control" readonly 
                                       value="<?= date('F j, Y g:i A', strtotime($client['updated_at'])) ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between mt-4">
                <a href="<?= base_url('clients/' . $client['id']) ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Client
                </a>
                <div>
                    <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                        <i class="fas fa-redo"></i>
                        Reset Changes
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Update Client
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function resetForm() {
    if (confirm('Are you sure you want to reset the form? All changes will be lost.')) {
        location.reload();
    }
}

// Auto-format phone number (basic formatting)
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length >= 10) {
        value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    }
    e.target.value = value;
});

// Auto-capitalize names
document.getElementById('name').addEventListener('blur', function(e) {
    e.target.value = e.target.value.replace(/\b\w/g, l => l.toUpperCase());
});

document.getElementById('city').addEventListener('blur', function(e) {
    e.target.value = e.target.value.replace(/\b\w/g, l => l.toUpperCase());
});

document.getElementById('state').addEventListener('blur', function(e) {
    e.target.value = e.target.value.replace(/\b\w/g, l => l.toUpperCase());
});

document.getElementById('country').addEventListener('blur', function(e) {
    e.target.value = e.target.value.replace(/\b\w/g, l => l.toUpperCase());
});
</script>
<?= $this->endSection() ?>
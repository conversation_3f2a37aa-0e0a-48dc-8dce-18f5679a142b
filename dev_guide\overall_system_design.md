# Dakoii Accounts System - System Design Document

## 1. System Overview

The Dakoii Accounts System is a comprehensive financial management platform that combines AI-powered invoice generation, payment tracking, budget management, and financial reporting capabilities.

### Core Architecture
- **Framework**: CodeIgniter 4 (MV<PERSON> Pattern)
- **Frontend**: Bootstrap 5 + DataTables.js
- **Database**: MySQL
- **File Storage**: Google Cloud Storage (20MB max)
- **AI Integration**: For invoice generation
- **Authentication**: Role-based access control

## 2. Database Schema Design

```sql
-- Users and Authentication
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'supervisor', 'user') DEFAULT 'user',
    first_name VARCHAR(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Client/Customer Management
CREATE TABLE clients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(50),
    state VARCHAR(50),
    zip_code VARCHAR(10),
    country VARCHAR(50),
    tax_id VARCHAR(50),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Invoice Management
CREATE TABLE invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    invoice_type ENUM('invoice', 'quote') NOT NULL,
    client_id INT NOT NULL,
    user_id INT NOT NULL,
    issue_date DATE NOT NULL,
    due_date DATE,
    subtotal DECIMAL(12,2) DEFAULT 0.00,
    tax_rate DECIMAL(5,2) DEFAULT 0.00,
    tax_amount DECIMAL(12,2) DEFAULT 0.00,
    discount_amount DECIMAL(12,2) DEFAULT 0.00,
    total_amount DECIMAL(12,2) NOT NULL,
    status ENUM('draft', 'sent', 'viewed', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
    notes TEXT,
    terms_conditions TEXT,
    ai_generated BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Invoice Line Items
CREATE TABLE invoice_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    description TEXT NOT NULL,
    quantity DECIMAL(8,2) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    line_total DECIMAL(12,2) NOT NULL,
    sort_order INT DEFAULT 0,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
);

-- Payment Management
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    payment_number VARCHAR(50) UNIQUE NOT NULL,
    invoice_id INT,
    amount DECIMAL(12,2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method ENUM('cash', 'check', 'bank_transfer', 'credit_card', 'other') NOT NULL,
    reference_number VARCHAR(100),
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Budget Management
CREATE TABLE budgets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    total_amount DECIMAL(12,2) NOT NULL,
    allocated_amount DECIMAL(12,2) DEFAULT 0.00,
    spent_amount DECIMAL(12,2) DEFAULT 0.00,
    remaining_amount DECIMAL(12,2) GENERATED ALWAYS AS (total_amount - spent_amount) STORED,
    budget_period_start DATE NOT NULL,
    budget_period_end DATE NOT NULL,
    status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Budget Categories/Items
CREATE TABLE budget_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    budget_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    allocated_amount DECIMAL(12,2) NOT NULL,
    spent_amount DECIMAL(12,2) DEFAULT 0.00,
    remaining_amount DECIMAL(12,2) GENERATED ALWAYS AS (allocated_amount - spent_amount) STORED,
    description TEXT,
    FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE CASCADE
);

-- Budget Transactions (Spending from budget)
CREATE TABLE budget_transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    budget_id INT NOT NULL,
    budget_category_id INT,
    payment_id INT,
    transaction_type ENUM('expense', 'allocation', 'transfer') NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    description TEXT NOT NULL,
    transaction_date DATE NOT NULL,
    reference_number VARCHAR(100),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (budget_id) REFERENCES budgets(id),
    FOREIGN KEY (budget_category_id) REFERENCES budget_categories(id),
    FOREIGN KEY (payment_id) REFERENCES payments(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- File Attachments
CREATE TABLE attachments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entity_type ENUM('invoice', 'payment', 'budget', 'transaction') NOT NULL,
    entity_id INT NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    gcs_bucket VARCHAR(100),
    gcs_object_name VARCHAR(500),
    uploaded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);

-- System Settings
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Audit Log
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 3. Application Architecture

### 3.1 Directory Structure
```
app/
├── Controllers/
│   ├── Auth/
│   │   ├── LoginController.php
│   │   └── LogoutController.php
│   ├── Invoice/
│   │   ├── InvoiceController.php
│   │   ├── InvoiceApiController.php
│   │   └── AIInvoiceController.php
│   ├── Payment/
│   │   ├── PaymentController.php
│   │   └── PaymentApiController.php
│   ├── Budget/
│   │   ├── BudgetController.php
│   │   ├── BudgetCategoryController.php
│   │   └── BudgetTransactionController.php
│   ├── Report/
│   │   └── ReportController.php
│   ├── Admin/
│   │   ├── UserController.php
│   │   └── SystemController.php
│   └── DashboardController.php
├── Models/
│   ├── UserModel.php
│   ├── ClientModel.php
│   ├── InvoiceModel.php
│   ├── InvoiceItemModel.php
│   ├── PaymentModel.php
│   ├── BudgetModel.php
│   ├── BudgetCategoryModel.php
│   ├── BudgetTransactionModel.php
│   ├── AttachmentModel.php
│   └── AuditLogModel.php
├── Services/
│   ├── AIService.php
│   ├── InvoiceService.php
│   ├── PaymentService.php
│   ├── BudgetService.php
│   ├── FileUploadService.php
│   ├── GoogleCloudStorageService.php
│   ├── NotificationService.php
│   └── AuditService.php
├── Filters/
│   ├── AuthFilter.php
│   ├── RoleFilter.php
│   └── CorsFilter.php
├── Views/
│   ├── auth/
│   ├── dashboard/
│   ├── invoice/
│   ├── payment/
│   ├── budget/
│   ├── reports/
│   ├── admin/
│   └── layouts/
└── Config/
    ├── Routes.php
    ├── Filters.php
    └── Database.php
```

### 3.2 Core Services

#### AIService.php
```php
<?php
namespace App\Services;

class AIService
{
    private $apiKey;
    private $apiEndpoint;
    
    public function generateInvoice($items, $clientInfo, $preferences = [])
    {
        // AI integration for invoice generation
        // Process items and generate formatted invoice
        // Return structured invoice data
    }
    
    public function suggestInvoiceItems($description)
    {
        // AI-powered item suggestion
    }
    
    public function extractInvoiceData($fileContent)
    {
        // AI extraction from uploaded documents
    }
}
```

#### GoogleCloudStorageService.php
```php
<?php
namespace App\Services;

use Google\Cloud\Storage\StorageClient;

class GoogleCloudStorageService
{
    private $storage;
    private $bucketName;
    
    public function uploadFile($file, $directory = '')
    {
        // Upload file to GCS
        // Return file URL and metadata
    }
    
    public function deleteFile($fileName)
    {
        // Delete file from GCS
    }
    
    public function getFileUrl($fileName)
    {
        // Generate signed URL for file access
    }
}
```

## 4. Feature Implementation

### 4.1 Invoice Feature with AI

**Key Components:**
- AI-powered invoice generation
- Template management
- PDF generation
- Email sending
- Invoice tracking

**API Endpoints:**
```
POST /api/invoices/generate-ai
POST /api/invoices/create
GET /api/invoices
GET /api/invoices/{id}
PUT /api/invoices/{id}
DELETE /api/invoices/{id}
POST /api/invoices/{id}/send
```

### 4.2 Payment Feature

**Key Components:**
- Payment recording
- Invoice payment matching
- Payment methods
- Receipt generation

**API Endpoints:**
```
POST /api/payments
GET /api/payments
GET /api/payments/{id}
PUT /api/payments/{id}
DELETE /api/payments/{id}
POST /api/payments/{id}/link-invoice
```

### 4.3 Budget Feature

**Key Components:**
- Budget creation and management
- Category-based spending
- Transaction tracking
- Budget analytics

**Workflow:**
1. Dashboard → Budget Management
2. Create/Edit Budget
3. Allocate to Categories
4. Record Transactions
5. Track Spending vs Budget

## 5. User Interface Design

### 5.1 Dashboard Components

```html
<!-- Bootstrap 5 Dashboard Layout -->
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-2 sidebar">
            <nav class="nav flex-column">
                <a class="nav-link" href="/dashboard">Dashboard</a>
                <a class="nav-link" href="/invoices">Invoices</a>
                <a class="nav-link" href="/payments">Payments</a>
                <a class="nav-link" href="/budgets">Budgets</a>
                <a class="nav-link" href="/reports">Reports</a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-10">
            <!-- KPI Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <h5>Total Revenue</h5>
                            <h2 id="total-revenue">$0</h2>
                        </div>
                    </div>
                </div>
                <!-- More KPI cards -->
            </div>
            
            <!-- DataTables Integration -->
            <div class="card">
                <div class="card-header">
                    <h5>Recent Invoices</h5>
                </div>
                <div class="card-body">
                    <table id="invoices-table" class="table table-striped">
                        <thead>
                            <tr>
                                <th>Invoice #</th>
                                <th>Client</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
```

### 5.2 DataTables Configuration

```javascript
// Invoice DataTable
$('#invoices-table').DataTable({
    processing: true,
    serverSide: true,
    ajax: {
        url: '/api/invoices/datatable',
        type: 'POST'
    },
    columns: [
        { data: 'invoice_number' },
        { data: 'client_name' },
        { data: 'total_amount', render: function(data) {
            return '$' + parseFloat(data).toFixed(2);
        }},
        { data: 'status', render: function(data) {
            return '<span class="badge bg-' + getStatusColor(data) + '">' + data + '</span>';
        }},
        { data: 'actions', orderable: false }
    ],
    order: [[0, 'desc']],
    pageLength: 25,
    responsive: true
});
```

## 6. Security Implementation

### 6.1 Authentication & Authorization

```php
// AuthFilter.php
class AuthFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        if (!session()->get('user_id')) {
            return redirect()->to('/login');
        }
    }
}

// RoleFilter.php
class RoleFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        $requiredRole = $arguments[0] ?? 'user';
        $userRole = session()->get('user_role');
        
        if (!$this->hasPermission($userRole, $requiredRole)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
    }
}
```

### 6.2 Role-Based Permissions

| Feature | Admin | Supervisor | User |
|---------|--------|------------|------|
| User Management | Full | View Only | None |
| Invoice Creation | Full | Full | Limited |
| Payment Management | Full | Full | View Only |
| Budget Management | Full | Full | Assigned Only |
| Reports | Full | Full | Limited |
| System Settings | Full | None | None |

## 7. API Design

### 7.1 RESTful API Structure

```php
// Routes.php
$routes->group('api', ['namespace' => 'App\Controllers\API'], function($routes) {
    // Authentication
    $routes->post('auth/login', 'AuthController::login');
    $routes->post('auth/logout', 'AuthController::logout');
    
    // Invoices
    $routes->resource('invoices', ['controller' => 'InvoiceController']);
    $routes->post('invoices/generate-ai', 'InvoiceController::generateWithAI');
    
    // Payments
    $routes->resource('payments', ['controller' => 'PaymentController']);
    
    // Budgets
    $routes->resource('budgets', ['controller' => 'BudgetController']);
    $routes->resource('budget-transactions', ['controller' => 'BudgetTransactionController']);
});
```

### 7.2 Response Format

```json
{
    "success": true,
    "message": "Invoice created successfully",
    "data": {
        "id": 123,
        "invoice_number": "INV-2024-001",
        "total_amount": 1500.00,
        "status": "draft"
    },
    "meta": {
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_records": 125
        }
    }
}
```

## 8. File Management

### 8.1 Upload Configuration

```php
// FileUploadService.php
class FileUploadService
{
    const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
    const ALLOWED_TYPES = ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'xls', 'xlsx'];
    
    public function uploadToGCS($file, $directory = 'documents')
    {
        // Validate file
        $this->validateFile($file);
        
        // Upload to Google Cloud Storage
        $gcsService = new GoogleCloudStorageService();
        return $gcsService->uploadFile($file, $directory);
    }
}
```

## 9. Reporting System

### 9.1 Available Reports

1. **Financial Reports**
   - Income Statement
   - Cash Flow Report
   - Accounts Receivable Aging
   - Budget vs Actual

2. **Invoice Reports**
   - Invoice Summary
   - Outstanding Invoices
   - Payment History

3. **Budget Reports**
   - Budget Performance
   - Spending Analysis
   - Budget Variance Report

### 9.2 Export Formats

- PDF (using TCPDF)
- Excel (using PhpSpreadsheet)
- CSV

## 10. Deployment Architecture

### 10.1 Environment Configuration

```
Production Environment:
- Web Server: Apache/Nginx
- PHP: 8.1+
- Database: MySQL 8.0+
- File Storage: Google Cloud Storage
- SSL Certificate: Required
- Backup: Automated daily backups
```

### 10.2 Performance Optimization

- Database indexing on frequently queried columns
- Caching with Redis/Memcached
- CDN for static assets
- Image optimization
- Database query optimization
- API rate limiting

## 11. Testing Strategy

### 11.1 Testing Types

1. **Unit Tests**: Models and Services
2. **Integration Tests**: API endpoints
3. **Feature Tests**: User workflows
4. **Security Tests**: Authentication and authorization

### 11.2 Test Coverage Goals

- Models: 90%+
- Services: 85%+
- Controllers: 80%+
- Overall: 85%+

## 12. Maintenance & Monitoring

### 12.1 Logging

- Application logs
- Error logs
- Audit logs
- Performance logs

### 12.2 Monitoring

- System performance metrics
- Database performance
- File storage usage
- User activity tracking
- Error rate monitoring

This comprehensive system design provides a solid foundation for building the Dakoii Accounts System with all the specified features and requirements.
<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="page-header">
    <h1 class="page-title">Edit <?= ucfirst($invoice['invoice_type']) ?> #<?= esc($invoice['invoice_number']) ?></h1>
    <p class="page-subtitle">Update the details of this <?= $invoice['invoice_type'] ?></p>
</div>

<!-- Invoice Form -->
<form method="POST" action="<?= base_url('invoices/' . $invoice['id']) ?>" id="invoiceForm">
    <?= csrf_field() ?>
    <input type="hidden" name="_method" value="PUT">
    
    <div class="row">
        <!-- Left Column -->
        <div class="col-md-8">
            <!-- Basic Information -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Basic Information</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="invoice_type" class="form-label">Type <span class="text-danger">*</span></label>
                                <select name="invoice_type" id="invoice_type" class="form-select" required>
                                    <option value="invoice" <?= $invoice['invoice_type'] === 'invoice' ? 'selected' : '' ?>>Invoice</option>
                                    <option value="quotation" <?= $invoice['invoice_type'] === 'quotation' ? 'selected' : '' ?>>Quotation</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="client_id" class="form-label">Client <span class="text-danger">*</span></label>
                                <select name="client_id" id="client_id" class="form-select" required>
                                    <option value="">Select Client</option>
                                    <?php foreach ($clients as $clientId => $clientName): ?>
                                        <option value="<?= $clientId ?>" <?= $invoice['client_id'] == $clientId ? 'selected' : '' ?>>
                                            <?= esc($clientName) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="account_id" class="form-label">Account</label>
                                <select name="account_id" id="account_id" class="form-select">
                                    <option value="">Use Default Account</option>
                                    <?php foreach ($accounts as $accountId => $accountName): ?>
                                        <option value="<?= $accountId ?>" <?= $invoice['account_id'] == $accountId ? 'selected' : '' ?>>
                                            <?= esc($accountName) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <small class="text-muted">Select which organizational account to use for this invoice</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Empty column for spacing -->
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="issue_date" class="form-label">Issue Date <span class="text-danger">*</span></label>
                                <input type="date" name="issue_date" id="issue_date" class="form-control" value="<?= $invoice['issue_date'] ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="due_date" class="form-label">Due Date</label>
                                <input type="date" name="due_date" id="due_date" class="form-control" value="<?= $invoice['due_date'] ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Items -->
            <div class="admin-card mb-4">
                <div class="admin-card-header d-flex justify-content-between align-items-center">
                    <h5 class="admin-card-title">Items</h5>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addItem()">
                        <i class="fas fa-plus"></i> Add Item
                    </button>
                </div>
                <div class="admin-card-body">
                    <div id="items-container">
                        <?php foreach ($items as $index => $item): ?>
                            <div class="item-row mb-3" data-index="<?= $index ?>">
                                <div class="row align-items-end">
                                    <div class="col-md-5">
                                        <label class="form-label">Description <span class="text-danger">*</span></label>
                                        <textarea name="item_description[]" class="form-control" rows="2" placeholder="Item description" required><?= esc($item['description']) ?></textarea>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">Quantity <span class="text-danger">*</span></label>
                                        <input type="number" name="item_quantity[]" class="form-control quantity-input" step="0.01" min="0.01" value="<?= $item['quantity'] ?>" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">Unit Price <span class="text-danger">*</span></label>
                                        <input type="number" name="item_unit_price[]" class="form-control price-input" step="0.01" min="0" value="<?= $item['unit_price'] ?>" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">Total</label>
                                        <input type="text" class="form-control line-total" readonly value="$<?= number_format($item['line_total'], 2) ?>">
                                    </div>
                                    <div class="col-md-1">
                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeItem(this)" <?= count($items) === 1 ? 'style="visibility: hidden;"' : '' ?>>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Notes and Terms -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Additional Information</h5>
                </div>
                <div class="admin-card-body">
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="Internal notes (not visible to client)"><?= esc($invoice['notes']) ?></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="terms_conditions" class="form-label">Terms & Conditions</label>
                        <textarea name="terms_conditions" id="terms_conditions" class="form-control" rows="3" placeholder="Terms and conditions for this invoice"><?= esc($invoice['terms_conditions']) ?></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-md-4">
            <!-- Summary -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Summary</h5>
                </div>
                <div class="admin-card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span id="subtotal">$<?= number_format($invoice['subtotal'], 2) ?></span>
                    </div>
                    
                    <div class="mb-3">
                        <label for="discount_amount" class="form-label">Discount Amount</label>
                        <input type="number" name="discount_amount" id="discount_amount" class="form-control" step="0.01" min="0" value="<?= $invoice['discount_amount'] ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="tax_rate" class="form-label">Tax Rate (%)</label>
                        <input type="number" name="tax_rate" id="tax_rate" class="form-control" step="0.01" min="0" max="100" value="<?= $invoice['tax_rate'] ?>">
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tax Amount:</span>
                        <span id="tax_amount">$<?= number_format($invoice['tax_amount'], 2) ?></span>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <strong>Total:</strong>
                        <strong id="total_amount">$<?= number_format($invoice['total_amount'], 2) ?></strong>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-select">
                            <option value="draft" <?= $invoice['status'] === 'draft' ? 'selected' : '' ?>>Draft</option>
                            <option value="sent" <?= $invoice['status'] === 'sent' ? 'selected' : '' ?>>Sent</option>
                            <option value="viewed" <?= $invoice['status'] === 'viewed' ? 'selected' : '' ?>>Viewed</option>
                            <option value="accepted" <?= $invoice['status'] === 'accepted' ? 'selected' : '' ?>>Accepted</option>
                            <option value="paid" <?= $invoice['status'] === 'paid' ? 'selected' : '' ?>>Paid</option>
                            <option value="overdue" <?= $invoice['status'] === 'overdue' ? 'selected' : '' ?>>Overdue</option>
                            <option value="cancelled" <?= $invoice['status'] === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="admin-card">
                <div class="admin-card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Update <?= ucfirst($invoice['invoice_type']) ?>
                        </button>
                        <a href="<?= base_url('invoices/' . $invoice['id']) ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
let itemIndex = <?= count($items) ?>;

function addItem() {
    const container = document.getElementById('items-container');
    const itemHtml = `
        <div class="item-row mb-3" data-index="${itemIndex}">
            <div class="row align-items-end">
                <div class="col-md-5">
                    <label class="form-label">Description <span class="text-danger">*</span></label>
                    <textarea name="item_description[]" class="form-control" rows="2" placeholder="Item description" required></textarea>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Quantity <span class="text-danger">*</span></label>
                    <input type="number" name="item_quantity[]" class="form-control quantity-input" step="0.01" min="0.01" value="1" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Unit Price <span class="text-danger">*</span></label>
                    <input type="number" name="item_unit_price[]" class="form-control price-input" step="0.01" min="0" value="0" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Total</label>
                    <input type="text" class="form-control line-total" readonly value="$0.00">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeItem(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', itemHtml);
    itemIndex++;
    
    // Add event listeners to new inputs
    attachEventListeners();
    calculateTotals();
}

function removeItem(button) {
    const itemRow = button.closest('.item-row');
    itemRow.remove();
    calculateTotals();
    
    // Show/hide remove buttons based on item count
    const itemRows = document.querySelectorAll('.item-row');
    itemRows.forEach((row, index) => {
        const removeBtn = row.querySelector('.btn-outline-danger');
        if (itemRows.length === 1) {
            removeBtn.style.visibility = 'hidden';
        } else {
            removeBtn.style.visibility = 'visible';
        }
    });
}

function attachEventListeners() {
    // Remove existing listeners to avoid duplicates
    document.querySelectorAll('.quantity-input, .price-input, #discount_amount, #tax_rate').forEach(input => {
        input.removeEventListener('input', calculateTotals);
        input.addEventListener('input', calculateTotals);
    });
}

function calculateTotals() {
    let subtotal = 0;
    
    // Calculate line totals and subtotal
    document.querySelectorAll('.item-row').forEach(row => {
        const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
        const unitPrice = parseFloat(row.querySelector('.price-input').value) || 0;
        const lineTotal = quantity * unitPrice;
        
        row.querySelector('.line-total').value = '$' + lineTotal.toFixed(2);
        subtotal += lineTotal;
    });
    
    // Get discount and tax
    const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
    const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
    
    // Calculate tax amount
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = taxableAmount * (taxRate / 100);
    
    // Calculate total
    const total = subtotal + taxAmount - discountAmount;
    
    // Update display
    document.getElementById('subtotal').textContent = '$' + subtotal.toFixed(2);
    document.getElementById('tax_amount').textContent = '$' + taxAmount.toFixed(2);
    document.getElementById('total_amount').textContent = '$' + total.toFixed(2);
}

// Initialize event listeners and calculations
document.addEventListener('DOMContentLoaded', function() {
    attachEventListeners();
    calculateTotals();
    
    // Update form title when type changes
    document.getElementById('invoice_type').addEventListener('change', function() {
        const type = this.value;
        document.querySelector('.page-title').textContent = 'Edit ' + type.charAt(0).toUpperCase() + type.slice(1) + ' #<?= esc($invoice['invoice_number']) ?>';
        document.querySelector('.page-subtitle').textContent = 'Update the details of this ' + type;
        document.querySelector('button[type="submit"]').innerHTML = '<i class="fas fa-save"></i> Update ' + type.charAt(0).toUpperCase() + type.slice(1);
    });
    
});
</script>
<?= $this->endSection() ?>

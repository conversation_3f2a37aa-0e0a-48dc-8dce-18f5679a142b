<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">Client Management</h1>
        <p class="page-subtitle">Manage customers and clients for invoice and quotation generation</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('clients/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            Add New Client
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon">
                    <i class="fas fa-users text-primary"></i>
                </div>
                <h3 class="stat-number"><?= count($clients) ?></h3>
                <p class="stat-label">Total Clients</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon">
                    <i class="fas fa-user-check text-success"></i>
                </div>
                <h3 class="stat-number"><?= count(array_filter($clients, function($c) { return !empty($c['email']); })) ?></h3>
                <p class="stat-label">With Email</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon">
                    <i class="fas fa-phone text-info"></i>
                </div>
                <h3 class="stat-number"><?= count(array_filter($clients, function($c) { return !empty($c['phone']); })) ?></h3>
                <p class="stat-label">With Phone</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon">
                    <i class="fas fa-calendar text-warning"></i>
                </div>
                <h3 class="stat-number"><?= date('M Y') ?></h3>
                <p class="stat-label">Current Period</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="admin-card mb-4">
    <div class="admin-card-body">
        <form method="GET" action="<?= base_url('clients') ?>" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Search by name, email, or phone..." 
                       value="<?= esc($filters['search'] ?? '') ?>">
            </div>
            <div class="col-md-3">
                <label for="city" class="form-label">City</label>
                <input type="text" name="city" id="city" class="form-control" 
                       placeholder="Filter by city..." 
                       value="<?= esc($filters['city'] ?? '') ?>">
            </div>
            <div class="col-md-3">
                <label for="country" class="form-label">Country</label>
                <input type="text" name="country" id="country" class="form-control" 
                       placeholder="Filter by country..." 
                       value="<?= esc($filters['country'] ?? '') ?>">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search"></i>
                    Filter
                </button>
                <a href="<?= base_url('clients') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Clients Table -->
<div class="admin-card">
    <div class="admin-card-header">
        <h5 class="admin-card-title">Clients List</h5>
    </div>
    <div class="admin-card-body">
        <?php if (!empty($clients)): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Client Name</th>
                            <th>Contact Information</th>
                            <th>Location</th>
                            <th>Tax ID</th>
                            <th>Created By</th>
                            <th>Created Date</th>
                            <th width="150">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($clients as $client): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; background-color: #f8f9fa; border-radius: 4px;">
                                            <i class="fas fa-user text-muted"></i>
                                        </div>
                                        <div>
                                            <strong><?= esc($client['name']) ?></strong>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <?php if (!empty($client['email'])): ?>
                                            <div class="mb-1">
                                                <i class="fas fa-envelope text-muted"></i>
                                                <small><?= esc($client['email']) ?></small>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($client['phone'])): ?>
                                            <div>
                                                <i class="fas fa-phone text-muted"></i>
                                                <small><?= esc($client['phone']) ?></small>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (empty($client['email']) && empty($client['phone'])): ?>
                                            <small class="text-muted">No contact info</small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <?php if (!empty($client['city']) || !empty($client['country'])): ?>
                                            <div>
                                                <?php if (!empty($client['city'])): ?>
                                                    <?= esc($client['city']) ?>
                                                <?php endif; ?>
                                                <?php if (!empty($client['city']) && !empty($client['country'])): ?>
                                                    ,
                                                <?php endif; ?>
                                                <?php if (!empty($client['country'])): ?>
                                                    <?= esc($client['country']) ?>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($client['state'])): ?>
                                            <small class="text-muted"><?= esc($client['state']) ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($client['tax_id'])): ?>
                                        <span class="badge bg-light text-dark"><?= esc($client['tax_id']) ?></span>
                                    <?php else: ?>
                                        <small class="text-muted">-</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($client['first_name']) || !empty($client['last_name'])): ?>
                                        <small class="text-muted">
                                            <?= esc($client['first_name'] . ' ' . $client['last_name']) ?>
                                        </small>
                                    <?php else: ?>
                                        <small class="text-muted">System</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= date('M d, Y', strtotime($client['created_at'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('clients/' . $client['id']) ?>" 
                                           class="btn btn-sm btn-outline-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('clients/' . $client['id'] . '/edit') ?>" 
                                           class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="<?= base_url('clients/' . $client['id']) ?>" style="display: inline;">
                                            <?= csrf_field() ?>
                                            <input type="hidden" name="_method" value="DELETE">
                                            <button type="submit" class="btn btn-sm btn-outline-danger"
                                                    onclick="return confirm('Are you sure you want to delete this client? This action cannot be undone if the client has invoices.')"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($pager): ?>
                <div class="d-flex justify-content-center mt-4">
                    <?= $pager->links() ?>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5>No Clients Found</h5>
                <p class="text-muted">
                    <?php if (!empty($filters['search']) || !empty($filters['city']) || !empty($filters['country'])): ?>
                        No clients match your current filters. Try adjusting your search criteria.
                    <?php else: ?>
                        Start by adding your first client to begin managing customer relationships.
                    <?php endif; ?>
                </p>
                <a href="<?= base_url('clients/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Add First Client
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
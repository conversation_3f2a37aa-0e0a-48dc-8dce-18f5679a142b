# DAccounts Production Deployment Guide

## Overview
This guide will help you deploy the DAccounts application to the production server at `admin.dakoiims.com`.

## Prerequisites
- Database export file: `daccounts_db_export.sql` (✅ Created - 33KB)
- Production database configuration: `Database_production.php` (✅ Created)
- SSH access to production server
- Git repository access

## Server Credentials
- **Server:** sg1-ts2.a2hosting.com
- **Username:** dakoiim1
- **Password:** dakoiianzii
- **Target Directory:** admin.dakoiims.com/daccounts/

## Database Credentials
- **Host:** localhost (via SSH tunnel)
- **Username:** dakoiim1_dakoiim1_daccounts_admin
- **Password:** dakoiianzii
- **Database:** dakoiim1_daccounts_db

## Deployment Steps

### Step 1: Upload Database Export
```bash
# Option A: Using SCP
scp daccounts_db_export.sql <EMAIL>:~/

# Option B: Use FTP client to upload the file
```

### Step 2: Connect to Production Server
```bash
ssh <EMAIL>
# Enter password: dakoiianzii
```

### Step 3: Import Database
```bash
# Import the database
mysql -u dakoiim1_dakoiim1_daccounts_admin -p dakoiim1_daccounts_db < daccounts_db_export.sql
# Enter password: dakoiianzii
```

### Step 4: Navigate to Target Directory
```bash
cd admin.dakoiims.com/daccounts/
```

### Step 5: Clone GitHub Repository
```bash
# Clone the repository
git clone https://github.com/anziinols/daccounts.git .

# When prompted for credentials:
# Username: anziinols
# Password: *********************************************************************************************
```

### Step 6: Update Database Configuration
```bash
# Replace the database configuration
cp Database_production.php app/Config/Database.php

# Or manually edit app/Config/Database.php with these settings:
# hostname: localhost
# username: dakoiim1_dakoiim1_daccounts_admin
# password: dakoiianzii
# database: dakoiim1_daccounts_db
```

### Step 7: Set File Permissions
```bash
# Set proper permissions
chmod -R 755 .
chmod -R 777 writable/
```

### Step 8: Verify Deployment
1. Visit: `https://admin.dakoiims.com/daccounts/`
2. Test login functionality
3. Verify CRUD operations work
4. Check database connectivity

## Files Created for Deployment
- ✅ `daccounts_db_export.sql` - Database export (33KB)
- ✅ `Database_production.php` - Production database configuration
- ✅ `deployment_script.sh` - Linux deployment script
- ✅ `deploy_to_production.bat` - Windows deployment helper
- ✅ `DEPLOYMENT_GUIDE.md` - This comprehensive guide

## Troubleshooting
- If database import fails, check credentials and database name
- If git clone fails, verify GitHub credentials and repository access
- If application doesn't load, check file permissions and database configuration
- If database connection fails, verify the database configuration in `app/Config/Database.php`

## Post-Deployment Checklist
- [ ] Database imported successfully
- [ ] Code deployed to correct directory
- [ ] Database configuration updated
- [ ] File permissions set correctly
- [ ] Application loads in browser
- [ ] Login functionality works
- [ ] CRUD operations functional
- [ ] All features tested

## Support
If you encounter any issues during deployment, check the error logs and verify all credentials are correct.

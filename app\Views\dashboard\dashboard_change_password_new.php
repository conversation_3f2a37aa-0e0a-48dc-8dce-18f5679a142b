<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header">
    <h1 class="page-title">Change Password</h1>
    <p class="page-subtitle">Update your account password for security</p>
</div>

<!-- Change Password Form -->
<div class="admin-card">
    <div class="admin-card-header">
        <h5 class="admin-card-title">Password Update</h5>
    </div>
    <div class="admin-card-body">
        <form method="POST" action="<?= base_url('dashboard/updatePassword') ?>">
            <?= csrf_field() ?>
            
            <div class="mb-3">
                <label for="current_password" class="form-label">Current Password</label>
                <input type="password" class="form-control <?= session('validation') && session('validation')->hasError('current_password') ? 'is-invalid' : '' ?>" 
                       id="current_password" name="current_password" required>
                <?php if (session('validation') && session('validation')->hasError('current_password')): ?>
                    <div class="invalid-feedback">
                        <?= session('validation')->getError('current_password') ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="mb-3">
                <label for="new_password" class="form-label">New Password</label>
                <input type="password" class="form-control <?= session('validation') && session('validation')->hasError('new_password') ? 'is-invalid' : '' ?>" 
                       id="new_password" name="new_password" required>
                <div class="form-text">Password must be at least 8 characters long.</div>
                <?php if (session('validation') && session('validation')->hasError('new_password')): ?>
                    <div class="invalid-feedback">
                        <?= session('validation')->getError('new_password') ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="mb-3">
                <label for="confirm_password" class="form-label">Confirm New Password</label>
                <input type="password" class="form-control <?= session('validation') && session('validation')->hasError('confirm_password') ? 'is-invalid' : '' ?>" 
                       id="confirm_password" name="confirm_password" required>
                <?php if (session('validation') && session('validation')->hasError('confirm_password')): ?>
                    <div class="invalid-feedback">
                        <?= session('validation')->getError('confirm_password') ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-key"></i>
                    Update Password
                </button>
                <a href="<?= base_url('dashboard/profile') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Profile
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Security Tips -->
<div class="admin-card">
    <div class="admin-card-header">
        <h5 class="admin-card-title">Security Tips</h5>
    </div>
    <div class="admin-card-body">
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-shield-alt text-success"></i> Strong Password</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> At least 8 characters long</li>
                    <li><i class="fas fa-check text-success"></i> Mix of uppercase and lowercase letters</li>
                    <li><i class="fas fa-check text-success"></i> Include numbers and special characters</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-exclamation-triangle text-warning"></i> Security Best Practices</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Don't reuse passwords from other accounts</li>
                    <li><i class="fas fa-check text-success"></i> Change your password regularly</li>
                    <li><i class="fas fa-check text-success"></i> Never share your password with others</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

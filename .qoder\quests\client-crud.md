# Client CRUD Feature Design

## 1. Overview

The Client CRUD feature provides comprehensive client management functionality for the D'Accounts system, enabling users to create, read, update, and delete client records. This feature serves as a foundation for invoice and quotation generation, following RESTful architectural principles and maintaining data integrity through proper validation and soft deletion mechanisms.

### 1.1 Core Purpose

The client management system enables businesses to:
- Maintain a centralized database of customers and business partners
- Store essential contact and billing information
- Track client statistics and invoice relationships
- Support invoice and quotation generation workflows
- Ensure data integrity through validation and referential constraints

### 1.2 Business Value

- **Centralized Client Data**: Single source of truth for all client information
- **Invoice Integration**: Seamless client selection during invoice creation
- **Data Analytics**: Client statistics for business insights
- **Compliance**: Tax ID and address management for regulatory requirements
- **Operational Efficiency**: Quick client lookup and selection capabilities

## 2. Technology Stack & Dependencies

### 2.1 Backend Framework
- **CodeIgniter 4**: Primary PHP framework
- **MySQL**: Database management system
- **Model-View-Controller (MVC)**: Architectural pattern

### 2.2 Frontend Components
- **Bootstrap 5**: UI framework for responsive design
- **FontAwesome**: Icon library
- **JavaScript/jQuery**: Client-side interactions and AJAX functionality

### 2.3 Security Features
- **CSRF Protection**: Cross-site request forgery prevention
- **Authentication Filter**: Session-based access control
- **Input Validation**: Server-side data validation
- **SQL Injection Prevention**: Parameterized queries through ORM

## 3. Architecture

### 3.1 System Architecture Overview

```mermaid
graph TB
    subgraph "Client Layer"
        A[Web Browser]
        B[AJAX Requests]
    end
    
    subgraph "Presentation Layer"
        C[Client Views]
        D[Admin Layout]
        E[Form Components]
    end
    
    subgraph "Controller Layer"
        F[Client Controller]
        G[Authentication Filter]
        H[Validation Logic]
    end
    
    subgraph "Model Layer"
        I[Client Model]
        J[Invoice Model]
        K[User Model]
    end
    
    subgraph "Data Layer"
        L[(Clients Table)]
        M[(Invoices Table)]
        N[(Users Table)]
    end
    
    A --> C
    B --> F
    C --> F
    F --> G
    G --> H
    H --> I
    I --> L
    I --> J
    J --> M
    I --> K
    K --> N
```

### 3.2 RESTful API Structure

The client management system implements standard RESTful conventions:

| HTTP Method | Endpoint | Controller Method | Purpose |
|-------------|----------|-------------------|---------|
| GET | `/clients` | `index()` | List all clients with pagination |
| GET | `/clients/create` | `create()` | Display client creation form |
| POST | `/clients` | `store()` | Create new client record |
| GET | `/clients/{id}` | `show($id)` | Display specific client details |
| GET | `/clients/{id}/edit` | `edit($id)` | Display client edit form |
| PUT/PATCH | `/clients/{id}` | `update($id)` | Update existing client |
| DELETE | `/clients/{id}` | `destroy($id)` | Soft delete client |
| POST | `/clients/{id}/restore` | `restore($id)` | Restore deleted client |
| GET | `/clients/search` | `search()` | AJAX client search endpoint |

### 3.3 Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant C as Controller
    participant M as Model
    participant DB as Database
    
    Note over U,DB: Create Client Flow
    U->>C: POST /clients (form data)
    C->>C: Validate CSRF token
    C->>C: Authenticate user
    C->>M: insert(clientData)
    M->>M: Validate input data
    M->>M: Set created_by field
    M->>DB: INSERT INTO clients
    DB-->>M: Return insert ID
    M-->>C: Success/Failure response
    C-->>U: Redirect with flash message
    
    Note over U,DB: Read Client Flow
    U->>C: GET /clients/{id}
    C->>C: Authenticate user
    C->>M: getClientWithStats(id)
    M->>DB: SELECT with JOIN invoices
    DB-->>M: Client data with statistics
    M-->>C: Client object
    C-->>U: Render client view
```

## 4. Data Models & ORM Mapping

### 4.1 Client Entity Structure

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | INT(11) | PRIMARY KEY, AUTO_INCREMENT | Unique client identifier |
| name | VARCHAR(100) | NOT NULL | Client business or individual name |
| email | VARCHAR(100) | NULLABLE, UNIQUE | Primary email address |
| phone | VARCHAR(20) | NULLABLE | Primary phone number |
| address | TEXT | NULLABLE | Street address |
| city | VARCHAR(50) | NULLABLE | City location |
| state | VARCHAR(50) | NULLABLE | State or province |
| zip_code | VARCHAR(20) | NULLABLE | Postal code |
| country | VARCHAR(50) | NULLABLE | Country name |
| tax_id | VARCHAR(50) | NULLABLE | Tax identification number |
| created_by | INT(11) | NOT NULL, FK to users.id | User who created the record |
| created_at | TIMESTAMP | AUTO | Record creation timestamp |
| updated_at | TIMESTAMP | AUTO | Last modification timestamp |
| is_deleted | BOOLEAN | DEFAULT FALSE | Soft deletion flag |
| deleted_at | TIMESTAMP | NULLABLE | Soft deletion timestamp |
| deleted_by | INT(11) | NULLABLE, FK to users.id | User who deleted the record |

### 4.2 Data Relationships

```mermaid
erDiagram
    CLIENTS {
        int id PK
        string name
        string email
        string phone
        text address
        string city
        string state
        string zip_code
        string country
        string tax_id
        int created_by FK
        timestamp created_at
        timestamp updated_at
        boolean is_deleted
        timestamp deleted_at
        int deleted_by FK
    }
    
    INVOICES {
        int id PK
        int client_id FK
        string invoice_number
        decimal total_amount
        string status
        date issue_date
        date due_date
    }
    
    USERS {
        int id PK
        string first_name
        string last_name
        string email
        string role
    }
    
    CLIENTS ||--o{ INVOICES : "has many"
    USERS ||--o{ CLIENTS : "creates"
    USERS ||--o{ CLIENTS : "deletes"
```

### 4.3 Model Validation Rules

| Field | Validation Rules | Error Messages |
|-------|------------------|----------------|
| name | required, max_length[100] | "Client name is required", "Cannot exceed 100 characters" |
| email | permit_empty, valid_email, max_length[100] | "Invalid email format", "Cannot exceed 100 characters" |
| phone | permit_empty, max_length[20] | "Cannot exceed 20 characters" |
| tax_id | permit_empty, max_length[50] | "Cannot exceed 50 characters" |

## 5. API Endpoints Reference

### 5.1 List Clients Endpoint

**GET /clients**

**Purpose**: Retrieve paginated list of clients with filtering capabilities

**Request Parameters**:
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| search | string | No | Search term for name, email, or phone |
| city | string | No | Filter by city |
| country | string | No | Filter by country |
| page | integer | No | Page number for pagination |

**Response Structure**:
```
{
  "clients": [
    {
      "id": 1,
      "name": "Client Name",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "city": "New York",
      "country": "USA",
      "created_at": "2024-01-15 10:30:00"
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 5,
    "per_page": 20,
    "total_records": 95
  }
}
```

### 5.2 Create Client Endpoint

**POST /clients**

**Purpose**: Create new client record

**Request Body**:
| Field | Type | Required | Validation |
|-------|------|----------|------------|
| name | string | Yes | max_length[100] |
| email | string | No | valid_email, max_length[100] |
| phone | string | No | max_length[20] |
| address | string | No | - |
| city | string | No | max_length[50] |
| state | string | No | max_length[50] |
| zip_code | string | No | max_length[20] |
| country | string | No | max_length[50] |
| tax_id | string | No | max_length[50] |

**Authentication Requirements**: Valid session, logged-in user

### 5.3 Show Client Details Endpoint

**GET /clients/{id}**

**Purpose**: Retrieve detailed client information with statistics

**Response Includes**:
- Complete client information
- Invoice statistics (total count, paid amount, outstanding amount)
- Creation and modification metadata

### 5.4 Update Client Endpoint

**PUT/PATCH /clients/{id}**

**Purpose**: Update existing client information

**Request Validation**: Same as create endpoint
**Business Rules**: 
- Client must exist
- User must be authenticated
- All validation rules apply

### 5.5 Delete Client Endpoint

**DELETE /clients/{id}**

**Purpose**: Soft delete client record

**Business Rules**:
- Cannot delete clients with active invoices
- Soft deletion preserves referential integrity
- Only authenticated users can delete
- Admin users can restore deleted clients

## 6. Business Logic Layer

### 6.1 Client Management Operations

#### 6.1.1 Client Creation Process

```mermaid
flowchart TD
    A[User Submits Form] --> B{Validate CSRF Token}
    B -->|Invalid| C[Return Error]
    B -->|Valid| D{Authenticate User}
    D -->|Failed| E[Redirect to Login]
    D -->|Success| F[Validate Input Data]
    F -->|Invalid| G[Return Validation Errors]
    F -->|Valid| H[Set Created By Field]
    H --> I[Insert to Database]
    I -->|Success| J[Redirect to Client Details]
    I -->|Failure| K[Return Database Error]
```

#### 6.1.2 Client Deletion Logic

```mermaid
flowchart TD
    A[Delete Request] --> B{Client Exists?}
    B -->|No| C[Return 404 Error]
    B -->|Yes| D{Has Active Invoices?}
    D -->|Yes| E[Block Deletion]
    D -->|No| F[Perform Soft Delete]
    F --> G[Update Deletion Fields]
    G --> H[Return Success Message]
```

### 6.2 Data Integrity Rules

1. **Referential Integrity**: Clients cannot be hard deleted if they have associated invoices
2. **Soft Deletion**: Maintains data history while hiding records from normal operations
3. **Audit Trail**: Tracks creation and deletion user information
4. **Validation**: Enforces data quality through model-level validation rules

### 6.3 Search and Filtering Logic

The system provides advanced search capabilities:

**Search Algorithm**:
- Multi-field search across name, email, and phone
- Partial string matching using SQL LIKE operators
- Geographic filtering by city and country
- Performance optimization through indexed fields

**Pagination Strategy**:
- Server-side pagination to handle large datasets
- Configurable page size (default: 20 records)
- URL-based pagination for bookmarkable results

## 7. State Management

### 7.1 Client Status States

```mermaid
stateDiagram-v2
    [*] --> Active: Create Client
    Active --> Active: Update Information
    Active --> SoftDeleted: Delete Client
    SoftDeleted --> Active: Restore Client
    SoftDeleted --> [*]: Hard Delete (Admin Only)
    
    note right of Active : Default state for normal operations
    note right of SoftDeleted : Hidden from normal views
```

### 7.2 Form State Management

**Client Creation Form**:
- Input validation feedback in real-time
- Form persistence on validation errors
- CSRF token regeneration on form display
- Success/error flash messages

**Client Edit Form**:
- Pre-population with existing data
- Change tracking for audit purposes
- Optimistic updates with rollback capability

## 8. Testing Strategy

### 8.1 Unit Testing Scope

**Model Testing**:
- Validation rule enforcement
- CRUD operation functionality
- Relationship integrity
- Soft deletion behavior
- Search and filtering logic

**Controller Testing**:
- HTTP method restrictions
- Authentication requirements
- Input validation handling
- Response format verification
- Error handling scenarios

### 8.2 Integration Testing

**Database Integration**:
- Foreign key constraint validation
- Transaction rollback scenarios
- Data consistency verification
- Performance benchmarking

**User Interface Testing**:
- Form submission workflows
- AJAX search functionality
- Pagination navigation
- Responsive design validation

### 8.3 Test Data Scenarios

| Scenario | Test Data | Expected Outcome |
|----------|-----------|------------------|
| Valid Client Creation | Complete form data | Success with redirect |
| Invalid Email Format | malformed@email | Validation error display |
| Duplicate Client Name | Existing name | Business rule validation |
| Client with Invoices Deletion | Active client with invoices | Deletion blocked |
| Search Functionality | Partial name match | Filtered results display |
| Pagination Limits | Large dataset | Proper page navigation |

## 9. Security Considerations

### 9.1 Authentication & Authorization

**Access Control**:
- Session-based authentication required for all operations
- Role-based permissions for sensitive operations (delete, restore)
- CSRF token validation on state-changing operations

**Data Protection**:
- Input sanitization to prevent XSS attacks
- SQL injection prevention through ORM parameterized queries
- Output encoding for user-generated content display

### 9.2 Data Validation Security

**Server-Side Validation**:
- All inputs validated regardless of client-side validation
- Length limits enforced to prevent buffer overflow attacks
- Email format validation to prevent header injection
- Special character filtering for security

### 9.3 Audit Trail Security

**Tracking Requirements**:
- User identification for all data modifications
- Timestamp recording for compliance requirements
- Soft deletion to maintain audit history
- Access logging for security monitoring

## 10. Performance Optimization

### 10.1 Database Performance

**Indexing Strategy**:
- Primary key optimization on id field
- Composite index on (is_deleted, name) for common queries
- Foreign key indexes for join operations
- Text search optimization for name/email fields

**Query Optimization**:
- Pagination to limit result sets
- Selective field loading for list views
- JOIN optimization for client statistics
- Caching strategy for frequently accessed data

### 10.2 Frontend Performance

**Resource Management**:
- Lazy loading for large client lists
- AJAX search to reduce page reloads
- Client-side form validation for immediate feedback
- Responsive image optimization for client logos

**User Experience Optimization**:
- Progressive form enhancement
- Loading indicators for slow operations
- Optimistic updates for immediate feedback
- Error state management for network issues

<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title"><?= esc($account['trading_name']) ?></h1>
        <p class="page-subtitle">
            <span class="status-badge status-<?= $account['status'] ?>">
                <?= ucfirst($account['status']) ?>
            </span>
            <?php if ($account['is_default']): ?>
                <span class="badge bg-warning ms-2">
                    <i class="fas fa-star"></i> Default Account
                </span>
            <?php endif; ?>
        </p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('accounts/' . $account['id'] . '/edit') ?>" class="btn btn-outline-primary">
            <i class="fas fa-edit"></i>
            Edit Account
        </a>
        <?php if (!$account['is_default']): ?>
            <a href="<?= base_url('accounts/' . $account['id'] . '/set-default') ?>" 
               class="btn btn-outline-warning" 
               onclick="return confirm('Set this account as default?')">
                <i class="fas fa-star"></i>
                Set as Default
            </a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <!-- Main Content -->
    <div class="col-md-8">
        <!-- Basic Information -->
        <div class="admin-card mb-4">
            <div class="admin-card-header">
                <h5 class="admin-card-title">Basic Information</h5>
            </div>
            <div class="admin-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Trading Name:</strong></td>
                                <td><?= esc($account['trading_name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Account Name:</strong></td>
                                <td><?= esc($account['account_name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Phone:</strong></td>
                                <td><?= esc($account['phone'] ?: 'Not provided') ?></td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td><?= esc($account['email'] ?: 'Not provided') ?></td>
                            </tr>
                            <tr>
                                <td><strong>Website:</strong></td>
                                <td>
                                    <?php if ($account['website']): ?>
                                        <a href="<?= esc($account['website']) ?>" target="_blank">
                                            <?= esc($account['website']) ?>
                                        </a>
                                    <?php else: ?>
                                        Not provided
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <strong>Address:</strong><br>
                        <?php if ($account['address']): ?>
                            <?= nl2br(esc($account['address'])) ?>
                        <?php else: ?>
                            <span class="text-muted">Not provided</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Banking Information -->
        <div class="admin-card mb-4">
            <div class="admin-card-header">
                <h5 class="admin-card-title">Banking Information</h5>
            </div>
            <div class="admin-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Account Number:</strong></td>
                                <td><?= esc($account['account_number']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Bank:</strong></td>
                                <td><?= esc($account['bank']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Bank Branch:</strong></td>
                                <td><?= esc($account['bank_branch'] ?: 'Not provided') ?></td>
                            </tr>
                            <tr>
                                <td><strong>SWIFT Code:</strong></td>
                                <td><?= esc($account['swift_code'] ?: 'Not provided') ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Legal Information -->
        <div class="admin-card mb-4">
            <div class="admin-card-header">
                <h5 class="admin-card-title">Legal Information</h5>
            </div>
            <div class="admin-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Tax Number:</strong></td>
                                <td><?= esc($account['tax_number'] ?: 'Not provided') ?></td>
                            </tr>
                            <tr>
                                <td><strong>Registration Number:</strong></td>
                                <td><?= esc($account['registration_number'] ?: 'Not provided') ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Branding Files -->
        <div class="admin-card mb-4">
            <div class="admin-card-header">
                <h5 class="admin-card-title">Branding Files</h5>
            </div>
            <div class="admin-card-body">
                <div class="mb-3">
                    <strong>Company Logo:</strong><br>
                   
                    <?php if ($account['logo']): ?>
                        <img src="<?= base_url($account['logo']) ?>" alt="Company Logo" class="img-fluid mt-2" style="max-height: 100px;">
                    <?php else: ?>
                        <span class="text-muted">Not uploaded</span>
                    <?php endif; ?>
                </div>
                
                <div class="mb-3">
                    <strong>Company Stamp:</strong><br>
                    <?php if ($account['stamp']): ?>
                        <img src="<?= base_url($account['stamp']) ?>" alt="Company Stamp" class="img-fluid mt-2" style="max-height: 100px;">
                    <?php else: ?>
                        <span class="text-muted">Not uploaded</span>
                    <?php endif; ?>
                </div>
                
                <div class="mb-3">
                    <strong>Authorized Signature:</strong><br>
                    <?php if ($account['signature']): ?>
                        <img src="<?= base_url($account['signature']) ?>" alt="Authorized Signature" class="img-fluid mt-2" style="max-height: 100px;">
                    <?php else: ?>
                        <span class="text-muted">Not uploaded</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <?php if (isset($account['stats'])): ?>
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">Invoice Statistics</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <h4 class="text-primary"><?= $account['stats']['total_invoices'] ?></h4>
                            <small class="text-muted">Total Invoices</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-success">$<?= number_format($account['stats']['total_paid'], 2) ?></h5>
                            <small class="text-muted">Total Paid</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-warning">$<?= number_format($account['stats']['total_outstanding'], 2) ?></h5>
                            <small class="text-muted">Outstanding</small>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Account Information -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h5 class="admin-card-title">Account Information</h5>
            </div>
            <div class="admin-card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <span class="status-badge status-<?= $account['status'] ?>">
                                <?= ucfirst($account['status']) ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Default:</strong></td>
                        <td>
                            <?php if ($account['is_default']): ?>
                                <span class="badge bg-warning">
                                    <i class="fas fa-star"></i> Yes
                                </span>
                            <?php else: ?>
                                <span class="text-muted">No</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Created:</strong></td>
                        <td><?= date('M d, Y', strtotime($account['created_at'])) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Updated:</strong></td>
                        <td><?= date('M d, Y', strtotime($account['updated_at'])) ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

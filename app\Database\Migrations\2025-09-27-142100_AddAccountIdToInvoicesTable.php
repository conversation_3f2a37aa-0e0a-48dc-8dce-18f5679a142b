<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddAccountIdToInvoicesTable extends Migration
{
    public function up()
    {
        // Add account_id field to invoices table
        $this->forge->addColumn('invoices', [
            'account_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => true,
                'after'      => 'user_id',
                'comment'    => 'Links invoice to organization account for branding and banking info'
            ]
        ]);
        
        // Add index for performance
        $this->forge->addKey('account_id', false, false, 'invoices');
        
        // Add foreign key constraint
        $this->forge->addForeignKey('account_id', 'accounts', 'id', 'SET NULL', 'SET NULL', 'invoices');
    }

    public function down()
    {
        // Drop foreign key first
        $this->forge->dropForeignKey('invoices', 'invoices_account_id_foreign');
        
        // Drop the column
        $this->forge->dropColumn('invoices', 'account_id');
    }
}

<?php

namespace App\Controllers;

use App\Models\ClientModel;
use CodeIgniter\HTTP\RedirectResponse;

class Client extends BaseController
{
    protected $clientModel;

    public function __construct()
    {
        $this->clientModel = new ClientModel();
    }

    /**
     * GET /clients - Display list of clients with pagination and filtering
     */
    public function index()
    {
        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login');
        }

        // Get filters from request
        $filters = [
            'search' => $this->request->getGet('search'),
            'city' => $this->request->getGet('city'),
            'country' => $this->request->getGet('country')
        ];

        // Get clients with pagination
        $perPage = 20;
        $clientsData = $this->clientModel->getClientsWithPagination($filters, $perPage);

        $data = [
            'title' => 'Client Management - Dakoii Accounts',
            'current_page' => 'clients',
            'clients' => $clientsData['data'],
            'pager' => $clientsData['pager'],
            'filters' => $filters,
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => base_url('dashboard')],
                ['title' => 'Client Management']
            ]
        ];

        return view('client/client_index', $data);
    }

    /**
     * GET /clients/create - Show create client form
     */
    public function create()
    {
        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login');
        }

        $data = [
            'title' => 'Create New Client - Dakoii Accounts',
            'current_page' => 'clients',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => base_url('dashboard')],
                ['title' => 'Client Management', 'url' => base_url('clients')],
                ['title' => 'Create Client']
            ]
        ];

        return view('client/client_create', $data);
    }

    /**
     * POST /clients - Store new client
     */
    public function store(): RedirectResponse
    {
        // Ensure this method only handles POST requests
        if (!$this->request->is('post')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }

        $data = $this->request->getPost();

        if ($this->clientModel->insert($data)) {
            session()->setFlashdata('success', 'Client created successfully.');
            return redirect()->to(base_url('clients/' . $this->clientModel->getInsertID()));
        } else {
            session()->setFlashdata('error', 'Failed to create client. Please check the form for errors.');
            session()->setFlashdata('validation', $this->clientModel->errors());
            return redirect()->back()->withInput();
        }
    }

    /**
     * GET /clients/{id} - Show specific client details
     */
    public function show(int $id)
    {
        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login');
        }

        $client = $this->clientModel->getClientWithStats($id);

        if (!$client) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Client not found');
        }

        $data = [
            'title' => $client['name'] . ' - Client Details - Dakoii Accounts',
            'current_page' => 'clients',
            'client' => $client,
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => base_url('dashboard')],
                ['title' => 'Client Management', 'url' => base_url('clients')],
                ['title' => $client['name']]
            ]
        ];

        return view('client/client_view', $data);
    }

    /**
     * GET /clients/{id}/edit - Show edit client form
     */
    public function edit(int $id)
    {
        // Check authentication
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login');
        }

        $client = $this->clientModel->find($id);

        if (!$client) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Client not found');
        }

        $data = [
            'title' => 'Edit ' . $client['name'] . ' - Dakoii Accounts',
            'current_page' => 'clients',
            'client' => $client,
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => base_url('dashboard')],
                ['title' => 'Client Management', 'url' => base_url('clients')],
                ['title' => $client['name'], 'url' => base_url('clients/' . $id)],
                ['title' => 'Edit']
            ]
        ];

        return view('client/client_edit', $data);
    }

    /**
     * PUT/PATCH /clients/{id} - Update client
     */
    public function update(int $id): RedirectResponse
    {
        // Ensure this method only handles PUT/PATCH requests
        if (!$this->request->is('put') && !$this->request->is('patch')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }

        $client = $this->clientModel->find($id);

        if (!$client) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Client not found');
        }

        $data = $this->request->getPost();

        if ($this->clientModel->update($id, $data)) {
            session()->setFlashdata('success', 'Client updated successfully.');
            return redirect()->to(base_url('clients/' . $id));
        } else {
            session()->setFlashdata('error', 'Failed to update client. Please check the form for errors.');
            session()->setFlashdata('validation', $this->clientModel->errors());
            return redirect()->back()->withInput();
        }
    }

    /**
     * DELETE /clients/{id} - Delete client (soft delete)
     */
    public function destroy(int $id): RedirectResponse
    {
        // Ensure this method only handles DELETE requests
        if (!$this->request->is('delete')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }

        $client = $this->clientModel->find($id);

        if (!$client) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Client not found');
        }

        // Check if client has any invoices
        if ($this->clientModel->hasInvoices($id)) {
            session()->setFlashdata('error', 'Cannot delete client. This client has associated invoices.');
            return redirect()->to(base_url('clients/' . $id));
        }

        try {
            if ($this->clientModel->delete($id)) {
                session()->setFlashdata('success', 'Client deleted successfully.');
            } else {
                session()->setFlashdata('error', 'Failed to delete client.');
            }
        } catch (\RuntimeException $e) {
            session()->setFlashdata('error', $e->getMessage());
        }

        return redirect()->to(base_url('clients'));
    }

    /**
     * AJAX endpoint for client search
     */
    public function search()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $term = $this->request->getGet('term');
        $clients = $this->clientModel->searchClients($term);

        return $this->response->setJSON($clients);
    }

    /**
     * POST /clients/{id}/restore - Restore soft deleted client
     */
    public function restore(int $id): RedirectResponse
    {
        // Check if user has admin privileges
        if (session()->get('role') !== 'admin') {
            session()->setFlashdata('error', 'Access denied. Admin privileges required.');
            return redirect()->to(base_url('clients'));
        }

        if ($this->clientModel->restore($id)) {
            session()->setFlashdata('success', 'Client restored successfully.');
        } else {
            session()->setFlashdata('error', 'Failed to restore client.');
        }

        return redirect()->to(base_url('clients/' . $id));
    }
}
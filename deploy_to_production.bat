@echo off
echo ===============================================
echo DAccounts Production Deployment
echo ===============================================
echo.

echo Step 1: Database Export (COMPLETED)
echo - Database exported to: daccounts_db_export.sql
echo - File size: 33KB
echo.

echo Step 2: Manual Deployment Steps
echo ===============================
echo.

echo A. Upload Database Export:
echo   1. Upload daccounts_db_export.sql to server using FTP/SCP
echo   2. Or use: scp daccounts_db_export.sql <EMAIL>:~/
echo.

echo B. Connect to Production Server:
echo   ssh <EMAIL>
echo   Password: dakoiianzii
echo.

echo C. Import Database:
echo   mysql -u dakoiim1_dakoiim1_daccounts_admin -p dakoiim1_daccounts_db ^< daccounts_db_export.sql
echo   Password: dakoiianzii
echo.

echo D. Navigate to Target Directory:
echo   cd admin.dakoiims.com/daccounts/
echo.

echo E. Clone Repository:
echo   git clone https://github.com/anziinols/daccounts.git .
echo   Username: anziinols
echo   Password: *********************************************************************************************
echo.

echo F. Update Database Configuration:
echo   Replace app/Config/Database.php with Database_production.php content
echo.

echo G. Set Permissions:
echo   chmod -R 755 .
echo   chmod -R 777 writable/
echo.

echo H. Test Application:
echo   Visit: https://admin.dakoiims.com/daccounts/
echo.

echo ===============================================
echo Files Ready for Deployment:
echo - daccounts_db_export.sql (Database export)
echo - Database_production.php (Production DB config)
echo - deployment_script.sh (Linux deployment script)
echo ===============================================
echo.

pause

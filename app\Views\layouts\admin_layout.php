<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><?= $title ?? 'Dakoii Accounts - Admin' ?></title>
    <meta name="description" content="Dakoii Accounts - Company Accounting System">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" type="image/x-icon" href="<?= base_url('public/assets/icons/favicon.ico') ?>">
    <link rel="icon" type="image/x-icon" href="<?= base_url('public/assets/icons/favicon.ico') ?>">
    <link rel="apple-touch-icon" href="<?= base_url('public/assets/icons/favicon.ico') ?>">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2e8b57;
            --primary-light: #32cd32;
            --primary-dark: #228b22;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--light-color);
            min-height: 100vh;
        }

        /* Header */
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-section img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }

        .logo-section h1 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info {
            text-align: right;
        }

        .user-name {
            font-weight: 600;
            font-size: 1rem;
        }

        .user-role {
            font-size: 0.85rem;
            opacity: 0.9;
            text-transform: capitalize;
        }

        .header-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .header-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }

        /* Sidebar */
        .admin-sidebar {
            background: white;
            min-height: calc(100vh - 80px);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            padding: 2rem 0;
        }

        .sidebar-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-nav .nav-item {
            margin-bottom: 0.5rem;
        }

        .sidebar-nav .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            color: var(--dark-color);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            background: linear-gradient(90deg, rgba(46, 139, 87, 0.1), transparent);
            border-left-color: var(--primary-color);
            color: var(--primary-color);
        }

        .sidebar-nav .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .admin-main {
            padding: 2rem;
            min-height: calc(100vh - 80px);
        }

        /* Page Header */
        .page-header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e9ecef;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--secondary-color);
            font-size: 1rem;
        }

        .page-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Breadcrumb */
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin-bottom: 1rem;
        }

        .breadcrumb-item a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: var(--secondary-color);
        }

        /* Alert Messages */
        .alert {
            border: none;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Cards */
        .admin-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
            margin-bottom: 2rem;
        }

        .admin-card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .admin-card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--dark-color);
            margin: 0;
        }

        .admin-card-body {
            padding: 1.5rem;
        }

        /* Buttons */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(50, 205, 50, 0.3);
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        }

        .btn-outline-primary {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        /* Tables */
        .table {
            margin-bottom: 0;
        }

        .table th {
            background: var(--light-color);
            border-top: none;
            font-weight: 600;
            color: var(--dark-color);
            padding: 1rem;
        }

        .table td {
            padding: 1rem;
            vertical-align: middle;
        }

        /* Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-draft { background: #e9ecef; color: #6c757d; }
        .status-sent { background: #cce5ff; color: #0066cc; }
        .status-paid { background: #d4edda; color: #155724; }
        .status-overdue { background: #f8d7da; color: #721c24; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .admin-sidebar {
                min-height: auto;
            }

            .admin-main {
                padding: 1rem;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .page-actions {
                width: 100%;
                justify-content: flex-start;
            }
        }

        /* Loading States */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        /* AI Assistant Modal Styles */
        .ai-assistant-modal .modal-header {
            background: linear-gradient(135deg, var(--info-color), #20c997);
            color: white;
            border-bottom: none;
        }

        .ai-assistant-modal .modal-title {
            font-weight: 600;
        }

        .ai-assistant-modal .btn-close {
            filter: brightness(0) invert(1);
        }

        .ai-assistant-modal .alert-info {
            background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(32, 201, 151, 0.1));
            border: 1px solid rgba(23, 162, 184, 0.2);
            color: var(--info-color);
        }

        .btn-info {
            background: linear-gradient(135deg, var(--info-color), #20c997);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(23, 162, 184, 0.3);
            background: linear-gradient(135deg, #138496, #1aa082);
            color: white;
        }
    </style>

    <?= $this->renderSection('styles') ?>
</head>
<body>
    <!-- Header -->
    <header class="admin-header">
        <div class="container-fluid">
            <div class="header-content">
                <div class="logo-section">
                    <img src="<?= base_url('public/assets/images/logo.png') ?>" alt="Dakoii Accounts Logo">
                    <h1>DAKOII ACCOUNTS</h1>
                </div>
                <div class="user-section">
                    <div class="user-info">
                        <div class="user-name"><?= esc(session()->get('first_name') . ' ' . session()->get('last_name')) ?></div>
                        <div class="user-role"><?= esc(session()->get('role')) ?></div>
                    </div>
                    <a href="<?= base_url('dashboard') ?>" class="header-btn">
                        <i class="fas fa-home"></i>
                        Dashboard
                    </a>
                    <a href="<?= base_url('auth/logout') ?>" class="header-btn">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 p-0">
                <nav class="admin-sidebar">
                    <ul class="sidebar-nav">
                        <li class="nav-item">
                            <a href="<?= base_url('dashboard') ?>" class="nav-link <?= $current_page === 'dashboard' ? 'active' : '' ?>">
                                <i class="fas fa-home"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?= base_url('invoices') ?>" class="nav-link <?= $current_page === 'invoices' ? 'active' : '' ?>">
                                <i class="fas fa-file-invoice"></i>
                                <span>Invoices</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?= base_url('accounts') ?>" class="nav-link <?= $current_page === 'accounts' ? 'active' : '' ?>">
                                <i class="fas fa-building"></i>
                                <span>Accounts</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?= base_url('clients') ?>" class="nav-link <?= $current_page === 'clients' ? 'active' : '' ?>">
                                <i class="fas fa-users"></i>
                                <span>Clients</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?= base_url('reports') ?>" class="nav-link <?= $current_page === 'reports' ? 'active' : '' ?>">
                                <i class="fas fa-chart-bar"></i>
                                <span>Reports</span>
                            </a>
                        </li>
                        <?php if (session()->get('role') === 'admin'): ?>
                        <li class="nav-item">
                            <a href="<?= base_url('dashboard/users') ?>" class="nav-link <?= $current_page === 'users' ? 'active' : '' ?>">
                                <i class="fas fa-user-cog"></i>
                                <span>User Management</span>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10">
                <main class="admin-main">
                    <!-- Breadcrumb -->
                    <?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <?php foreach ($breadcrumbs as $breadcrumb): ?>
                                <?php if (isset($breadcrumb['url'])): ?>
                                    <li class="breadcrumb-item">
                                        <a href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['title'] ?></a>
                                    </li>
                                <?php else: ?>
                                    <li class="breadcrumb-item active" aria-current="page"><?= $breadcrumb['title'] ?></li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ol>
                    </nav>
                    <?php endif; ?>

                    <!-- Flash Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            <?= session()->getFlashdata('success') ?>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i>
                            <?= session()->getFlashdata('error') ?>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('warning')): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <?= session()->getFlashdata('warning') ?>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('info')): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <?= session()->getFlashdata('info') ?>
                        </div>
                    <?php endif; ?>

                    <!-- Page Content -->
                    <?= $this->renderSection('content') ?>
                </main>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // Global JavaScript functions
        function showLoading(element) {
            element.classList.add('loading');
            const spinner = element.querySelector('.spinner-border') || document.createElement('span');
            spinner.className = 'spinner-border spinner-border-sm me-2';
            element.prepend(spinner);
        }

        function hideLoading(element) {
            element.classList.remove('loading');
            const spinner = element.querySelector('.spinner-border');
            if (spinner) spinner.remove();
        }

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });
        });
    </script>

    <?= $this->renderSection('scripts') ?>
</body>
</html>

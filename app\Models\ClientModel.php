<?php

namespace App\Models;

use CodeIgniter\Model;

class ClientModel extends Model
{
    protected $table            = 'clients';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'name',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'zip_code',
        'country',
        'tax_id',
        'created_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Soft Delete Configuration
    protected $deletedByField = 'deleted_by';
    protected $isDeletedField = 'is_deleted';

    // Validation
    protected $validationRules = [
        'name' => [
            'label' => 'Client Name',
            'rules' => 'required|max_length[100]',
            'errors' => [
                'required' => 'Client name is required.',
                'max_length' => 'Client name cannot exceed 100 characters.'
            ]
        ],
        'email' => [
            'label' => 'Email',
            'rules' => 'permit_empty|valid_email|max_length[100]',
            'errors' => [
                'valid_email' => 'Please enter a valid email address.',
                'max_length' => 'Email cannot exceed 100 characters.'
            ]
        ],
        'phone' => [
            'label' => 'Phone',
            'rules' => 'permit_empty|max_length[20]',
            'errors' => [
                'max_length' => 'Phone number cannot exceed 20 characters.'
            ]
        ]
    ];

    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedBy'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = ['setSoftDeleteFields'];
    protected $afterDelete    = [];

    /**
     * Set created_by field before insert
     */
    protected function setCreatedBy(array $data)
    {
        if (!isset($data['data']['created_by'])) {
            $data['data']['created_by'] = session()->get('user_id');
        }
        
        return $data;
    }

    /**
     * Set soft delete fields before delete
     */
    protected function setSoftDeleteFields(array $data)
    {
        $data['data'][$this->deletedByField] = session()->get('user_id');
        $data['data'][$this->isDeletedField] = true;
        
        return $data;
    }

    /**
     * Override delete method to handle soft deletes
     */
    public function delete($id = null, bool $purge = false)
    {
        if ($purge) {
            return parent::delete($id, true);
        }

        // Soft delete
        $updateData = [
            $this->deletedField => date($this->dateFormat === 'int' ? 'U' : 'Y-m-d H:i:s'),
            $this->deletedByField => session()->get('user_id'),
            $this->isDeletedField => true
        ];

        return $this->update($id, $updateData);
    }

    /**
     * Get clients for dropdown/select options
     */
    public function getClientOptions(): array
    {
        $clients = $this->select('id, name')
                        ->where('is_deleted', false)
                        ->orderBy('name', 'ASC')
                        ->findAll();

        $options = [];
        foreach ($clients as $client) {
            $options[$client['id']] = $client['name'];
        }

        return $options;
    }

    /**
     * Search clients by name or email
     */
    public function searchClients(string $term, int $limit = 10): array
    {
        return $this->select('id, name, email, phone')
                    ->where('is_deleted', false)
                    ->groupStart()
                        ->like('name', $term)
                        ->orLike('email', $term)
                    ->groupEnd()
                    ->orderBy('name', 'ASC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Get client with invoice count
     */
    public function getClientWithStats(int $clientId): array|null
    {
        $client = $this->find($clientId);

        if (!$client) {
            return null;
        }

        // Get invoice statistics
        $invoiceModel = new \App\Models\InvoiceModel();
        $stats = $invoiceModel->select('
                COUNT(*) as total_invoices,
                SUM(CASE WHEN status = "paid" THEN total_amount ELSE 0 END) as total_paid,
                SUM(CASE WHEN status IN ("sent", "viewed") THEN total_amount ELSE 0 END) as total_outstanding
            ')
            ->where('client_id', $clientId)
            ->where('is_deleted', false)
            ->first();

        $client['stats'] = $stats ?: [
            'total_invoices' => 0,
            'total_paid' => 0.00,
            'total_outstanding' => 0.00
        ];

        return $client;
    }

    /**
     * Get active clients count
     */
    public function getActiveClientsCount(): int
    {
        return $this->where('is_deleted', false)->countAllResults();
    }

    /**
     * Create new client with validation
     */
    public function createClient(array $clientData): int|false
    {
        if ($this->insert($clientData)) {
            return $this->getInsertID();
        }
        
        return false;
    }

    /**
     * Update client information
     */
    public function updateClient(int $clientId, array $clientData): bool
    {
        return $this->update($clientId, $clientData);
    }

    /**
     * Check if client has any invoices
     */
    public function hasInvoices(int $clientId): bool
    {
        $invoiceModel = new \App\Models\InvoiceModel();
        return $invoiceModel->where('client_id', $clientId)
                           ->where('is_deleted', false)
                           ->countAllResults() > 0;
    }

    /**
     * Get clients with pagination and filtering
     */
    public function getClientsWithPagination(array $filters = [], int $perPage = 20): array
    {
        $builder = $this->select('clients.*, users.first_name, users.last_name')
                        ->join('users', 'users.id = clients.created_by', 'left')
                        ->where('clients.is_deleted', false);

        // Apply filters
        if (!empty($filters['search'])) {
            $builder->groupStart()
                    ->like('clients.name', $filters['search'])
                    ->orLike('clients.email', $filters['search'])
                    ->orLike('clients.phone', $filters['search'])
                    ->groupEnd();
        }

        if (!empty($filters['city'])) {
            $builder->where('clients.city', $filters['city']);
        }

        if (!empty($filters['country'])) {
            $builder->where('clients.country', $filters['country']);
        }

        $builder->orderBy('clients.name', 'ASC');

        return [
            'data' => $builder->paginate($perPage),
            'pager' => $this->pager
        ];
    }

    /**
     * Restore soft deleted client
     */
    public function restore(int $clientId): bool
    {
        return $this->update($clientId, [
            $this->deletedField => null,
            $this->deletedByField => null,
            $this->isDeletedField => false
        ]);
    }
}

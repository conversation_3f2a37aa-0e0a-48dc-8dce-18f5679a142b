<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title"><?= esc($user['first_name'] . ' ' . $user['last_name']) ?></h1>
        <p class="page-subtitle">
            <span class="status-badge status-<?= $user['status'] ?>">
                <?= ucfirst($user['status']) ?>
            </span>
            <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : 'primary' ?> ms-2">
                <?= ucfirst($user['role']) ?>
            </span>
        </p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('dashboard/users/' . $user['id'] . '/edit') ?>" class="btn btn-outline-primary">
            <i class="fas fa-edit"></i>
            Edit User
        </a>
        <a href="<?= base_url('dashboard/users') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i>
            Back to Users
        </a>
    </div>
</div>

<div class="row">
    <!-- Main Content -->
    <div class="col-md-8">
        <!-- User Information -->
        <div class="admin-card mb-4">
            <div class="admin-card-header">
                <h5 class="admin-card-title">User Information</h5>
            </div>
            <div class="admin-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>First Name:</strong></td>
                                <td><?= esc($user['first_name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Last Name:</strong></td>
                                <td><?= esc($user['last_name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td><?= esc($user['email']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Role:</strong></td>
                                <td>
                                    <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : 'primary' ?>">
                                        <?= ucfirst($user['role']) ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="status-badge status-<?= $user['status'] ?>">
                                        <?= ucfirst($user['status']) ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td><?= date('M d, Y \a\t g:i A', strtotime($user['created_at'])) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Last Updated:</strong></td>
                                <td><?= date('M d, Y \a\t g:i A', strtotime($user['updated_at'])) ?></td>
                            </tr>
                            <tr>
                                <td><strong>User ID:</strong></td>
                                <td><?= esc($user['id']) ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="admin-card mb-4">
            <div class="admin-card-header">
                <h5 class="admin-card-title">Quick Actions</h5>
            </div>
            <div class="admin-card-body">
                <div class="d-grid gap-2">
                    <a href="<?= base_url('dashboard/users/' . $user['id'] . '/edit') ?>" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i>
                        Edit User
                    </a>
                    <?php if ($user['id'] != session()->get('user_id')): ?>
                        <form method="POST" action="<?= base_url('dashboard/users/' . $user['id']) ?>" style="display: inline;">
                            <?= csrf_field() ?>
                            <input type="hidden" name="_method" value="DELETE">
                            <button type="submit" class="btn btn-outline-danger w-100"
                                    onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                <i class="fas fa-trash"></i>
                                Delete User
                            </button>
                        </form>
                    <?php else: ?>
                        <button type="button" class="btn btn-outline-secondary w-100" disabled>
                            <i class="fas fa-info-circle"></i>
                            Cannot Delete Own Account
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- User Statistics -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h5 class="admin-card-title">User Statistics</h5>
            </div>
            <div class="admin-card-body">
                <div class="text-center">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-user fa-3x text-primary"></i>
                    </div>
                    <p class="text-muted">
                        User has been active since<br>
                        <strong><?= date('M d, Y', strtotime($user['created_at'])) ?></strong>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?php

namespace App\Controllers;

use App\Models\AccountModel;
use CodeIgniter\HTTP\RedirectResponse;

class Account extends BaseController
{
    protected $accountModel;

    public function __construct()
    {
        $this->accountModel = new AccountModel();
    }

    /**
     * US-ACC-001: View Account List
     * Display paginated list of accounts with search and filtering
     */
    public function index()
    {
        $filters = [
            'search' => $this->request->getGet('search'),
            'status' => $this->request->getGet('status')
        ];

        $data = $this->accountModel->getAccountsPaginated(10, $filters);
        
        $viewData = [
            'title' => 'Account Management',
            'accounts' => $data['accounts'],
            'pager' => $data['pager'],
            'filters' => $filters,
            'total_accounts' => $this->accountModel->where('is_deleted', false)->countAllResults(),
            'active_accounts' => $this->accountModel->where('is_deleted', false)->where('status', 'active')->countAllResults(),
            'default_account' => $this->accountModel->getDefaultAccount(),
            'current_page' => 'accounts'
        ];

        return view('account/account_index', $viewData);
    }

    /**
     * US-ACC-002: Create New Account
     * Display form for creating new account
     */
    public function create()
    {
        $viewData = [
            'title' => 'Create New Account',
            'account' => [],
            'validation' => \Config\Services::validation(),
            'current_page' => 'accounts'
        ];

        return view('account/account_create', $viewData);
    }

    /**
     * US-ACC-002: Store New Account (RESTful approach)
     * Process form submission for creating new account
     * Only handles POST requests
     */
    public function store(): RedirectResponse
    {
        // Ensure this method only handles POST requests
        if (!$this->request->is('post')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }
        $data = $this->request->getPost();
        
        // Handle file uploads
        $data = $this->handleFileUploads($data);
        
        if ($this->accountModel->insert($data)) {
            session()->setFlashdata('success', 'Account created successfully.');
            return redirect()->to(base_url('accounts'));
        } else {
            session()->setFlashdata('error', 'Failed to create account. Please check the form for errors.');
            session()->setFlashdata('validation', $this->accountModel->errors());
            return redirect()->back()->withInput();
        }
    }

    /**
     * US-ACC-003: View Account Details
     * Display detailed view of a specific account
     */
    public function show(int $id)
    {
        $account = $this->accountModel->getAccountWithStats($id);
        
        if (!$account) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Account not found');
        }

        $viewData = [
            'title' => 'Account Details - ' . $account['trading_name'],
            'account' => $account,
            'current_page' => 'accounts'
        ];

        return view('account/account_view', $viewData);
    }

    /**
     * US-ACC-004: Edit Account
     * Display form for editing existing account
     */
    public function edit(int $id)
    {
        $account = $this->accountModel->find($id);
        
        if (!$account) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Account not found');
        }

        $viewData = [
            'title' => 'Edit Account - ' . $account['trading_name'],
            'account' => $account,
            'validation' => \Config\Services::validation(),
            'current_page' => 'accounts'
        ];

        return view('account/account_edit', $viewData);
    }

    /**
     * US-ACC-004: Update Account (RESTful approach)
     * Process form submission for updating account
     * Only handles PUT/PATCH requests
     */
    public function update(int $id): RedirectResponse
    {
        // Ensure this method only handles PUT/PATCH requests
        if (!$this->request->is('put') && !$this->request->is('patch')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }
        $account = $this->accountModel->find($id);
        
        if (!$account) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Account not found');
        }

        $data = $this->request->getPost();
        
        // Handle file uploads
        $data = $this->handleFileUploads($data, $account);
        
        if ($this->accountModel->update($id, $data)) {
            session()->setFlashdata('success', 'Account updated successfully.');
            return redirect()->to(base_url('accounts/' . $id));
        } else {
            session()->setFlashdata('error', 'Failed to update account. Please check the form for errors.');
            session()->setFlashdata('validation', $this->accountModel->errors());
            return redirect()->back()->withInput();
        }
    }

    /**
     * US-ACC-005: Delete Account (RESTful destroy method)
     * Soft delete account (with validation for invoice references)
     * Only handles DELETE requests
     */
    public function destroy(int $id): RedirectResponse
    {
        // Ensure this method only handles DELETE requests
        if (!$this->request->is('delete')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }
        $account = $this->accountModel->find($id);
        
        if (!$account) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Account not found');
        }

        try {
            if ($this->accountModel->delete($id)) {
                session()->setFlashdata('success', 'Account deleted successfully.');
            } else {
                session()->setFlashdata('error', 'Failed to delete account.');
            }
        } catch (\RuntimeException $e) {
            session()->setFlashdata('error', $e->getMessage());
        }

        return redirect()->to(base_url('accounts'));
    }

    /**
     * Set account as default (RESTful approach)
     * Only handles POST requests
     */
    public function setDefault(int $id): RedirectResponse
    {
        // Ensure this method only handles POST requests
        if (!$this->request->is('post')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }
        $account = $this->accountModel->find($id);
        
        if (!$account) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Account not found');
        }

        if ($this->accountModel->setAsDefault($id)) {
            session()->setFlashdata('success', 'Account set as default successfully.');
        } else {
            session()->setFlashdata('error', 'Failed to set account as default.');
        }

        return redirect()->to(base_url('accounts'));
    }

    /**
     * Handle file uploads for logo, stamp, and signature
     */
    private function handleFileUploads(array $data, array $existingAccount = []): array
    {
        // Use the public directory for uploads to ensure web accessibility
        $uploadPath = ROOTPATH . 'public/uploads/accounts/';

        // Create directory if it doesn't exist
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        $fileFields = ['logo', 'stamp', 'signature'];

        foreach ($fileFields as $field) {
            $file = $this->request->getFile($field);

            if ($file && $file->isValid() && !$file->hasMoved()) {
                // Validate file type
                if (!in_array($file->getExtension(), ['jpg', 'jpeg', 'png', 'gif'])) {
                    session()->setFlashdata('error', ucfirst($field) . ' must be a valid image file (jpg, png, gif).');
                    continue;
                }
                
                // Generate unique filename
                $fileName = $field . '_' . time() . '_' . $file->getRandomName();

                if ($file->move($uploadPath, $fileName)) {
                    // Delete old file if updating
                    if (!empty($existingAccount[$field])) {
                        $oldFile = ROOTPATH . $existingAccount[$field];
                        if (file_exists($oldFile)) {
                            unlink($oldFile);
                        }
                    }
                    
                    // Store relative path for web access
                    $data[$field] = 'public/uploads/accounts/' . $fileName;
                } else {
                    session()->setFlashdata('error', 'Failed to upload ' . $field . '. Error: ' . $file->getErrorString());
                }
            } elseif (!empty($existingAccount[$field])) {
                // Keep existing file if no new file uploaded
                $data[$field] = $existingAccount[$field];
            }
        }

        return $data;
    }
}

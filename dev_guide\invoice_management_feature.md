# Invoice Management Feature - User Story & Design

## 1. Feature Overview

The Invoice Management feature is a core component of the Dakoii Accounts System that enables users to create, manage, and track both invoices and quotations. This feature supports the complete lifecycle of invoice/quotation management from creation to payment tracking.

### 1.1 Feature Scope
- **Invoice Creation**: Create professional invoices with line items, taxes, and discounts
- **Quotation Management**: Generate quotations that can be converted to invoices
- **CRUD Operations**: Full Create, Read, Update, Delete functionality
- **Status Tracking**: Track invoice/quotation status throughout their lifecycle
- **Client Management**: Link invoices/quotations to clients
- **PDF Generation**: Generate professional PDF documents
- **Email Integration**: Send invoices/quotations directly to clients

## 2. User Stories

### 2.1 Primary User Stories

#### US-INV-001: Create New Invoice
**As a** user  
**I want to** create a new invoice with multiple line items  
**So that** I can bill my clients for services or products provided  

**Acceptance Criteria:**
- User can select invoice type (Invoice or Quotation)
- User can select or create a new client
- User can add multiple line items with description, quantity, and unit price
- System automatically calculates line totals, subtotal, tax, and grand total
- User can add notes and terms & conditions
- User can save as draft or mark as ready to send
- System generates unique invoice/quotation number automatically

#### US-INV-002: View Invoice List
**As a** user  
**I want to** view a list of all my invoices and quotations  
**So that** I can track and manage my billing activities  

**Acceptance Criteria:**
- User can view paginated list of invoices/quotations
- User can filter by type (Invoice/Quotation), status, client, date range
- User can search by invoice number, client name, or amount
- User can sort by any column (date, amount, status, etc.)
- User can see key information: number, client, amount, status, date
- User can access quick actions: view, edit, delete, send

#### US-INV-003: Edit Existing Invoice
**As a** user  
**I want to** edit an existing invoice or quotation  
**So that** I can make corrections or updates before sending  

**Acceptance Criteria:**
- User can edit all invoice fields (client, items, amounts, notes)
- User can add or remove line items
- System recalculates totals automatically
- User cannot edit invoices that are already paid
- System maintains audit trail of changes
- User can change status (draft, sent, etc.)

#### US-INV-004: Delete Invoice
**As a** user  
**I want to** delete an invoice or quotation  
**So that** I can remove incorrect or unwanted entries  

**Acceptance Criteria:**
- User can delete invoices in draft status
- User cannot delete invoices that have payments
- System shows confirmation dialog before deletion
- System maintains audit log of deletions
- Related line items are automatically deleted

#### US-INV-005: Convert Quotation to Invoice
**As a** user  
**I want to** convert an accepted quotation to an invoice  
**So that** I can proceed with billing the client  

**Acceptance Criteria:**
- User can convert quotation to invoice with one click
- System creates new invoice with same line items and client
- Original quotation status is updated to "Converted"
- New invoice gets unique invoice number
- User can modify invoice before finalizing

### 2.2 Secondary User Stories

#### US-INV-006: Generate PDF
**As a** user  
**I want to** generate a professional PDF of my invoice/quotation  
**So that** I can send it to clients or keep for records  

#### US-INV-007: Send via Email
**As a** user  
**I want to** send invoices/quotations directly via email  
**So that** I can deliver them to clients efficiently  

#### US-INV-008: Track Invoice Status
**As a** user  
**I want to** track the status of my invoices  
**So that** I can follow up on payments and manage cash flow  

## 3. Technical Design

### 3.1 Database Schema

Based on the overall system design, the invoice management feature uses these tables:

```sql
-- Main invoices table
CREATE TABLE invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    invoice_type ENUM('invoice', 'quotation') NOT NULL,
    client_id INT NOT NULL,
    user_id INT NOT NULL,
    issue_date DATE NOT NULL,
    due_date DATE,
    subtotal DECIMAL(12,2) DEFAULT 0.00,
    tax_rate DECIMAL(5,2) DEFAULT 0.00,
    tax_amount DECIMAL(12,2) DEFAULT 0.00,
    discount_amount DECIMAL(12,2) DEFAULT 0.00,
    total_amount DECIMAL(12,2) NOT NULL,
    status ENUM('draft', 'sent', 'viewed', 'accepted', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
    notes TEXT,
    terms_conditions TEXT,
    converted_from_id INT NULL, -- For quotations converted to invoices
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (converted_from_id) REFERENCES invoices(id)
);

-- Invoice line items
CREATE TABLE invoice_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    description TEXT NOT NULL,
    quantity DECIMAL(8,2) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    line_total DECIMAL(12,2) NOT NULL,
    sort_order INT DEFAULT 0,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
);

-- Clients table (if not exists)
CREATE TABLE clients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(50),
    state VARCHAR(50),
    zip_code VARCHAR(10),
    country VARCHAR(50),
    tax_id VARCHAR(50),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

### 3.2 MVC Architecture

#### 3.2.1 Models

**InvoiceModel.php**
- Handles all invoice CRUD operations
- Manages invoice number generation
- Calculates totals and taxes
- Handles status transitions

**InvoiceItemModel.php**
- Manages invoice line items
- Handles bulk insert/update/delete operations
- Calculates line totals

**ClientModel.php**
- Manages client information
- Provides client lookup functionality

#### 3.2.2 Controllers

**InvoiceController.php**
- `index()` - Display invoice list with filtering/searching
- `create()` - Show create invoice form
- `store()` - Save new invoice
- `show($id)` - Display single invoice
- `edit($id)` - Show edit form
- `update($id)` - Update existing invoice
- `delete($id)` - Delete invoice
- `convertToInvoice($id)` - Convert quotation to invoice
- `generatePDF($id)` - Generate PDF document
- `sendEmail($id)` - Send invoice via email

#### 3.2.3 Views

**invoice/invoice_index.php**
- List view with DataTables integration
- Filter and search functionality
- Quick action buttons

**invoice/invoice_create.php**
- Form for creating new invoice/quotation
- Dynamic line item addition
- Client selection/creation

**invoice/invoice_edit.php**
- Edit form with pre-populated data
- Status management
- Line item modification

**invoice/invoice_view.php**
- Read-only invoice display
- PDF preview
- Action buttons (edit, delete, send)

### 3.3 Business Logic

#### 3.3.1 Invoice Number Generation
```php
// Format: INV-YYYY-NNNN or QUO-YYYY-NNNN
public function generateInvoiceNumber($type = 'invoice')
{
    $prefix = $type === 'quotation' ? 'QUO' : 'INV';
    $year = date('Y');
    
    $lastNumber = $this->where('invoice_type', $type)
                       ->where('YEAR(created_at)', $year)
                       ->orderBy('id', 'DESC')
                       ->first();
    
    $nextNumber = $lastNumber ? 
        intval(substr($lastNumber['invoice_number'], -4)) + 1 : 1;
    
    return $prefix . '-' . $year . '-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
}
```

#### 3.3.2 Total Calculation
```php
public function calculateTotals($items, $taxRate = 0, $discountAmount = 0)
{
    $subtotal = array_sum(array_column($items, 'line_total'));
    $taxAmount = ($subtotal - $discountAmount) * ($taxRate / 100);
    $total = $subtotal + $taxAmount - $discountAmount;
    
    return [
        'subtotal' => $subtotal,
        'tax_amount' => $taxAmount,
        'total_amount' => $total
    ];
}
```

### 3.4 Status Workflow

#### 3.4.1 Invoice Status Flow
```
Draft → Sent → Viewed → Paid
  ↓       ↓       ↓       ↓
Cancelled ← ← ← ← ← ← Overdue
```

#### 3.4.2 Quotation Status Flow
```
Draft → Sent → Viewed → Accepted → [Convert to Invoice]
  ↓       ↓       ↓         ↓
Cancelled ← ← ← ← ← ← ← Rejected
```

## 4. User Interface Design

### 4.1 Navigation Integration
- Add "Invoice Management" to main navigation
- Quick access button on dashboard
- Breadcrumb navigation within invoice section

### 4.2 List View Features
- DataTables with server-side processing
- Advanced filtering (type, status, date range, client)
- Bulk actions (delete, send, export)
- Responsive design for mobile devices

### 4.3 Form Design
- Step-by-step wizard for complex invoices
- Auto-save functionality
- Real-time total calculations
- Client quick-add modal
- Line item drag-and-drop reordering

### 4.4 PDF Template
- Professional invoice template
- Company branding integration
- Multiple template options
- Customizable fields and layout

## 5. API Endpoints

### 5.1 RESTful API Design
```
GET    /api/invoices              - List invoices with pagination/filtering
POST   /api/invoices              - Create new invoice
GET    /api/invoices/{id}         - Get single invoice
PUT    /api/invoices/{id}         - Update invoice
DELETE /api/invoices/{id}         - Delete invoice
POST   /api/invoices/{id}/convert - Convert quotation to invoice
POST   /api/invoices/{id}/send    - Send invoice via email
GET    /api/invoices/{id}/pdf     - Generate PDF
```

### 5.2 Response Format
```json
{
    "success": true,
    "message": "Invoice created successfully",
    "data": {
        "id": 123,
        "invoice_number": "INV-2024-0001",
        "invoice_type": "invoice",
        "client": {
            "id": 45,
            "name": "ABC Company Ltd"
        },
        "total_amount": 1500.00,
        "status": "draft",
        "items": [
            {
                "description": "Web Development Services",
                "quantity": 40,
                "unit_price": 37.50,
                "line_total": 1500.00
            }
        ]
    }
}
```

## 6. Security Considerations

### 6.1 Access Control
- Users can only access their own invoices
- Admin users can view all invoices
- Role-based permissions for different actions

### 6.2 Data Validation
- Server-side validation for all inputs
- CSRF protection on all forms
- SQL injection prevention
- XSS protection for user inputs

### 6.3 Audit Trail
- Log all invoice modifications
- Track user actions and timestamps
- Maintain history of status changes

## 7. Performance Considerations

### 7.1 Database Optimization
- Proper indexing on frequently queried columns
- Pagination for large datasets
- Efficient joins for related data

### 7.2 Caching Strategy
- Cache frequently accessed invoice data
- PDF generation caching
- Client data caching

## 8. Testing Strategy

### 8.1 Unit Tests
- Model validation and calculations
- Business logic testing
- API endpoint testing

### 8.2 Integration Tests
- End-to-end invoice creation workflow
- PDF generation testing
- Email sending functionality

### 8.3 User Acceptance Testing
- Invoice creation scenarios
- Quotation to invoice conversion
- Multi-user access testing

This comprehensive design provides a solid foundation for implementing the Invoice Management feature with full CRUD functionality for both invoices and quotations.

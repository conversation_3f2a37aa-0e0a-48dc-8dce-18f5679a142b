<?= $this->extend('templates/dashboard_template') ?>

<?= $this->section('dashboard_content') ?>
<!-- Quick Access Section -->
<div class="quick-access-section">
    <h2 class="section-title">Quick Access</h2>
    <div class="quick-access-grid">
        <!-- Invoice Management Feature Box -->
        <div class="feature-box featured">
            <div class="feature-icon">
                <i class="fas fa-file-invoice-dollar"></i>
            </div>
            <div class="feature-content">
                <h3 class="feature-title">Invoice Management</h3>
                <p class="feature-description">Create, manage, and track invoices and quotations. Generate professional documents and monitor payment status.</p>
                <div class="feature-stats">
                    <div class="stat">
                        <span class="stat-number">0</span>
                        <span class="stat-label">Draft Invoices</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">0</span>
                        <span class="stat-label">Pending Quotes</span>
                    </div>
                </div>
                <a href="<?= base_url('invoices') ?>" class="feature-btn">
                    <i class="fas fa-arrow-right"></i>
                    Access Invoice Management
                </a>
            </div>
        </div>

        <!-- Account Management -->
        <div class="feature-box">
            <div class="feature-icon success">
                <i class="fas fa-building"></i>
            </div>
            <div class="feature-content">
                <h3 class="feature-title">Account Management</h3>
                <p class="feature-description">Manage organizational accounts for invoice branding and banking information.</p>
                <div class="feature-stats">
                    <div class="stat">
                        <span class="stat-number">0</span>
                        <span class="stat-label">Active Accounts</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">0</span>
                        <span class="stat-label">Default Set</span>
                    </div>
                </div>
                <a href="<?= base_url('accounts') ?>" class="feature-btn">
                    <i class="fas fa-arrow-right"></i>
                    Manage Accounts
                </a>
            </div>
        </div>

        <!-- Profile Management -->
        <div class="feature-box">
            <div class="feature-icon secondary">
                <i class="fas fa-user-cog"></i>
            </div>
            <div class="feature-content">
                <h3 class="feature-title">Profile Settings</h3>
                <p class="feature-description">Manage your account information and security settings.</p>
                <div class="feature-actions">
                    <a href="<?= base_url('dashboard/profile') ?>" class="feature-link">
                        <i class="fas fa-user"></i>
                        Edit Profile
                    </a>
                    <a href="<?= base_url('dashboard/change-password') ?>" class="feature-link">
                        <i class="fas fa-key"></i>
                        Change Password
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Grid -->
<div class="dashboard-grid">
    <!-- Welcome Card -->
    <div class="dashboard-card">
        <div class="card-header">
            <div class="card-icon welcome">
                <i class="fas fa-home"></i>
            </div>
            <h2 class="card-title">Welcome Back!</h2>
        </div>
        <div class="card-content">
            <p>Hello <strong><?= esc($user['first_name']) ?></strong>, welcome to your Dakoii Accounts dashboard. You can manage your accounting system from here.</p>
        </div>
    </div>

    <!-- Statistics Card -->
    <div class="dashboard-card">
        <div class="card-header">
            <div class="card-icon stats">
                <i class="fas fa-chart-bar"></i>
            </div>
            <h2 class="card-title">System Statistics</h2>
        </div>
        <div class="card-content">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?= $stats['total_users'] ?></div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= ucfirst($stats['user_role']) ?></div>
                    <div class="stat-label">Your Role</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Card -->
    <div class="dashboard-card">
        <div class="card-header">
            <div class="card-icon profile">
                <i class="fas fa-user-circle"></i>
            </div>
            <h2 class="card-title">Your Profile</h2>
        </div>
        <div class="card-content">
            <p><strong>Email:</strong> <?= esc($user['email']) ?></p>
            <p><strong>Role:</strong> <?= ucfirst(esc($user['role'])) ?></p>
            <p><strong>Status:</strong> <?= ucfirst(esc($user['status'])) ?></p>
            <div class="quick-actions">
                <?php if ($stats['user_role'] === 'admin'): ?>
                    <a href="<?= base_url('dashboard/users') ?>" class="action-btn">
                        <i class="fas fa-users"></i>
                        Manage Users
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">Invoice Management</h1>
        <p class="page-subtitle">Manage your invoices and quotations</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('invoices/create?type=quotation') ?>" class="btn btn-outline-primary">
            <i class="fas fa-file-alt"></i>
            New Quotation
        </a>
        <a href="<?= base_url('invoices/create?type=invoice') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            New Invoice
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon bg-primary text-white rounded-circle mx-auto mb-2" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <h3 class="mb-1"><?= $stats['total_invoices'] ?? 0 ?></h3>
                <p class="text-muted mb-0">Total Invoices</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon bg-info text-white rounded-circle mx-auto mb-2" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h3 class="mb-1"><?= $stats['total_quotations'] ?? 0 ?></h3>
                <p class="text-muted mb-0">Total Quotations</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon bg-warning text-white rounded-circle mx-auto mb-2" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-edit"></i>
                </div>
                <h3 class="mb-1"><?= $stats['draft_invoices'] ?? 0 ?></h3>
                <p class="text-muted mb-0">Draft Invoices</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon bg-success text-white rounded-circle mx-auto mb-2" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="mb-1"><?= $stats['paid_invoices'] ?? 0 ?></h3>
                <p class="text-muted mb-0">Paid Invoices</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="admin-card mb-4">
    <div class="admin-card-header">
        <h5 class="admin-card-title">Filters</h5>
    </div>
    <div class="admin-card-body">
        <form method="GET" action="<?= base_url('invoices') ?>" class="row g-3">
            <div class="col-md-2">
                <label for="type" class="form-label">Type</label>
                <select name="type" id="type" class="form-select">
                    <option value="">All Types</option>
                    <option value="invoice" <?= $filters['type'] === 'invoice' ? 'selected' : '' ?>>Invoice</option>
                    <option value="quotation" <?= $filters['type'] === 'quotation' ? 'selected' : '' ?>>Quotation</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="">All Status</option>
                    <option value="draft" <?= $filters['status'] === 'draft' ? 'selected' : '' ?>>Draft</option>
                    <option value="sent" <?= $filters['status'] === 'sent' ? 'selected' : '' ?>>Sent</option>
                    <option value="viewed" <?= $filters['status'] === 'viewed' ? 'selected' : '' ?>>Viewed</option>
                    <option value="accepted" <?= $filters['status'] === 'accepted' ? 'selected' : '' ?>>Accepted</option>
                    <option value="paid" <?= $filters['status'] === 'paid' ? 'selected' : '' ?>>Paid</option>
                    <option value="overdue" <?= $filters['status'] === 'overdue' ? 'selected' : '' ?>>Overdue</option>
                    <option value="cancelled" <?= $filters['status'] === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="client_id" class="form-label">Client</label>
                <select name="client_id" id="client_id" class="form-select">
                    <option value="">All Clients</option>
                    <?php foreach ($clients as $clientId => $clientName): ?>
                        <option value="<?= $clientId ?>" <?= $filters['client_id'] == $clientId ? 'selected' : '' ?>>
                            <?= esc($clientName) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">Date From</label>
                <input type="date" name="date_from" id="date_from" class="form-control" value="<?= esc($filters['date_from']) ?>">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">Date To</label>
                <input type="date" name="date_to" id="date_to" class="form-control" value="<?= esc($filters['date_to']) ?>">
            </div>
            <div class="col-md-2">
                <label for="search" class="form-label">Search</label>
                <div class="input-group">
                    <input type="text" name="search" id="search" class="form-control" placeholder="Invoice # or Client" value="<?= esc($filters['search']) ?>">
                    <button type="submit" class="btn btn-outline-secondary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Invoices Table -->
<div class="admin-card">
    <div class="admin-card-header">
        <h5 class="admin-card-title">Invoices & Quotations</h5>
    </div>
    <div class="admin-card-body">
        <?php if (empty($invoices)): ?>
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No invoices found</h5>
                <p class="text-muted">Create your first invoice or quotation to get started.</p>
                <a href="<?= base_url('invoices/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Create Invoice
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Number</th>
                            <th>Type</th>
                            <th>Client</th>
                            <th>Issue Date</th>
                            <th>Due Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($invoices as $invoice): ?>
                            <tr>
                                <td>
                                    <a href="<?= base_url('invoices/' . $invoice['id']) ?>" class="text-decoration-none">
                                        <strong><?= esc($invoice['invoice_number']) ?></strong>
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $invoice['invoice_type'] === 'invoice' ? 'primary' : 'info' ?>">
                                        <?= ucfirst($invoice['invoice_type']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?= esc($invoice['client_name']) ?>
                                    <?php if (!empty($invoice['client_email'])): ?>
                                        <br><small class="text-muted"><?= esc($invoice['client_email']) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?= date('M d, Y', strtotime($invoice['issue_date'])) ?></td>
                                <td>
                                    <?php if ($invoice['due_date']): ?>
                                        <?= date('M d, Y', strtotime($invoice['due_date'])) ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong>$<?= number_format($invoice['total_amount'], 2) ?></strong>
                                </td>
                                <td>
                                    <span class="status-badge status-<?= $invoice['status'] ?>">
                                        <?= ucfirst($invoice['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?= base_url('invoices/' . $invoice['id']) ?>" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info" title="Export PDF" onclick="exportInvoicePDF(<?= $invoice['id'] ?>)">
                                            <i class="fas fa-file-pdf"></i>
                                        </button>
                                        <?php if ($invoice['status'] !== 'paid'): ?>
                                            <a href="<?= base_url('invoices/' . $invoice['id'] . '/edit') ?>" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        <?php endif; ?>
                                        <?php if ($invoice['invoice_type'] === 'quotation' && in_array($invoice['status'], ['sent', 'viewed', 'accepted'])): ?>
                                            <a href="<?= base_url('invoices/' . $invoice['id'] . '/convert') ?>" class="btn btn-outline-success" title="Convert to Invoice" onclick="return confirm('Convert this quotation to an invoice?')">
                                                <i class="fas fa-exchange-alt"></i>
                                            </a>
                                        <?php endif; ?>
                                        <?php if ($invoice['status'] === 'draft'): ?>
                                            <form method="POST" action="<?= base_url('invoices/' . $invoice['id']) ?>" style="display: inline;">
                                                <?= csrf_field() ?>
                                                <input type="hidden" name="_method" value="DELETE">
                                                <button type="submit" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this <?= $invoice['invoice_type'] ?>?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($pager->getPageCount() > 1): ?>
                <div class="d-flex justify-content-center mt-4">
                    <?= $pager->links() ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Auto-submit form when filters change
    document.addEventListener('DOMContentLoaded', function() {
        const filterSelects = document.querySelectorAll('#type, #status, #client_id, #date_from, #date_to');
        filterSelects.forEach(select => {
            select.addEventListener('change', function() {
                this.form.submit();
            });
        });
    });

    /**
     * Export invoice as PDF using direct download approach
     * Following the PDF export guide implementation
     */
    function exportInvoicePDF(invoiceId) {
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;

        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        try {
            // Create direct link for GET request (no CSRF needed)
            const exportUrl = `<?= base_url('invoices/') ?>${invoiceId}/export-pdf`;
            
            // Open in new tab for download
            window.open(exportUrl, '_blank');

            // Restore button state after a short delay
            setTimeout(() => {
                button.innerHTML = originalContent;
                button.disabled = false;
            }, 1000);

        } catch (error) {
            console.error('PDF Export Error:', error);
            alert('Failed to export PDF. Please try again.');
            
            // Restore button state
            button.innerHTML = originalContent;
            button.disabled = false;
        }
    }
</script>
<?= $this->endSection() ?>

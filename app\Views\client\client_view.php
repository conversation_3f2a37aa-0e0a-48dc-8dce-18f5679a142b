<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title"><?= esc($client['name']) ?></h1>
        <p class="page-subtitle">Client Details and Information</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('clients/' . $client['id'] . '/edit') ?>" class="btn btn-primary">
            <i class="fas fa-edit"></i>
            Edit Client
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<?php if (isset($client['stats'])): ?>
<div class="row mb-4">
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon">
                    <i class="fas fa-file-invoice text-primary"></i>
                </div>
                <h3 class="stat-number"><?= $client['stats']['total_invoices'] ?></h3>
                <p class="stat-label">Total Invoices</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign text-success"></i>
                </div>
                <h3 class="stat-number">$<?= number_format($client['stats']['total_paid'], 2) ?></h3>
                <p class="stat-label">Total Paid</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon">
                    <i class="fas fa-clock text-warning"></i>
                </div>
                <h3 class="stat-number">$<?= number_format($client['stats']['total_outstanding'], 2) ?></h3>
                <p class="stat-label">Outstanding</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="admin-card">
            <div class="admin-card-body text-center">
                <div class="stat-icon">
                    <i class="fas fa-calendar text-info"></i>
                </div>
                <h3 class="stat-number"><?= date('M d, Y', strtotime($client['created_at'])) ?></h3>
                <p class="stat-label">Client Since</p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Client Information -->
<div class="row">
    <!-- Basic Information -->
    <div class="col-md-6">
        <div class="admin-card">
            <div class="admin-card-header">
                <h5 class="admin-card-title">
                    <i class="fas fa-user"></i>
                    Basic Information
                </h5>
            </div>
            <div class="admin-card-body">
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>Client Name:</strong>
                    </div>
                    <div class="col-sm-8">
                        <?= esc($client['name']) ?>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>Email:</strong>
                    </div>
                    <div class="col-sm-8">
                        <?php if (!empty($client['email'])): ?>
                            <a href="mailto:<?= esc($client['email']) ?>" class="text-decoration-none">
                                <i class="fas fa-envelope"></i>
                                <?= esc($client['email']) ?>
                            </a>
                        <?php else: ?>
                            <span class="text-muted">Not provided</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>Phone:</strong>
                    </div>
                    <div class="col-sm-8">
                        <?php if (!empty($client['phone'])): ?>
                            <a href="tel:<?= esc($client['phone']) ?>" class="text-decoration-none">
                                <i class="fas fa-phone"></i>
                                <?= esc($client['phone']) ?>
                            </a>
                        <?php else: ?>
                            <span class="text-muted">Not provided</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>Tax ID:</strong>
                    </div>
                    <div class="col-sm-8">
                        <?php if (!empty($client['tax_id'])): ?>
                            <span class="badge bg-light text-dark"><?= esc($client['tax_id']) ?></span>
                        <?php else: ?>
                            <span class="text-muted">Not provided</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Address Information -->
    <div class="col-md-6">
        <div class="admin-card">
            <div class="admin-card-header">
                <h5 class="admin-card-title">
                    <i class="fas fa-map-marker-alt"></i>
                    Address Information
                </h5>
            </div>
            <div class="admin-card-body">
                <?php if (!empty($client['address']) || !empty($client['city']) || !empty($client['state']) || !empty($client['country']) || !empty($client['zip_code'])): ?>
                    <div class="address-block">
                        <?php if (!empty($client['address'])): ?>
                            <div class="mb-2">
                                <strong>Street Address:</strong><br>
                                <?= nl2br(esc($client['address'])) ?>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($client['city']) || !empty($client['state']) || !empty($client['zip_code'])): ?>
                            <div class="mb-2">
                                <strong>City, State ZIP:</strong><br>
                                <?php
                                $location_parts = array_filter([
                                    $client['city'],
                                    $client['state'],
                                    $client['zip_code']
                                ]);
                                echo esc(implode(', ', $location_parts));
                                ?>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($client['country'])): ?>
                            <div class="mb-2">
                                <strong>Country:</strong><br>
                                <?= esc($client['country']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="text-muted text-center py-3">
                        <i class="fas fa-map-marker-alt fa-2x mb-2"></i>
                        <p>No address information provided</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Record Information -->
<div class="row mt-4">
    <div class="col-12">
        <div class="admin-card">
            <div class="admin-card-header">
                <h5 class="admin-card-title">
                    <i class="fas fa-info-circle"></i>
                    Record Information
                </h5>
            </div>
            <div class="admin-card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <strong>Client ID:</strong><br>
                            <span class="badge bg-secondary">#<?= $client['id'] ?></span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <strong>Created Date:</strong><br>
                            <span class="text-muted"><?= date('F j, Y g:i A', strtotime($client['created_at'])) ?></span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <strong>Last Updated:</strong><br>
                            <span class="text-muted"><?= date('F j, Y g:i A', strtotime($client['updated_at'])) ?></span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <strong>Created By:</strong><br>
                            <span class="text-muted">
                                <?php if (isset($client['created_by'])): ?>
                                    User ID: <?= $client['created_by'] ?>
                                <?php else: ?>
                                    System
                                <?php endif; ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between">
            <a href="<?= base_url('clients') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Back to Clients
            </a>
            <div>
                <a href="<?= base_url('invoices/create?client_id=' . $client['id']) ?>" class="btn btn-success me-2">
                    <i class="fas fa-file-invoice"></i>
                    Create Invoice
                </a>
                <a href="<?= base_url('clients/' . $client['id'] . '/edit') ?>" class="btn btn-primary me-2">
                    <i class="fas fa-edit"></i>
                    Edit Client
                </a>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-danger dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                        More Actions
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <form method="POST" action="<?= base_url('clients/' . $client['id']) ?>" class="dropdown-item p-0">
                                <?= csrf_field() ?>
                                <input type="hidden" name="_method" value="DELETE">
                                <button type="submit" class="btn btn-link text-danger text-decoration-none w-100 text-start"
                                        onclick="return confirm('Are you sure you want to delete this client? This action cannot be undone if the client has invoices.')">
                                    <i class="fas fa-trash"></i>
                                    Delete Client
                                </button>
                            </form>
                        </li>
                        <?php if (session()->get('role') === 'admin'): ?>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="<?= base_url('clients/' . $client['id'] . '/restore') ?>" class="dropdown-item p-0">
                                <?= csrf_field() ?>
                                <button type="submit" class="btn btn-link text-success text-decoration-none w-100 text-start">
                                    <i class="fas fa-undo"></i>
                                    Restore (Admin)
                                </button>
                            </form>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Add any client-specific JavaScript here
console.log('Client details page loaded for client ID: <?= $client['id'] ?>');
</script>
<?= $this->endSection() ?>
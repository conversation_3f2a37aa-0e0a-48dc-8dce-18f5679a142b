<?php

namespace App\Models;

use CodeIgniter\Model;

class InvoiceModel extends Model
{
    protected $table            = 'invoices';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'invoice_number',
        'invoice_type',
        'client_id',
        'user_id',
        'account_id',
        'issue_date',
        'due_date',
        'subtotal',
        'tax_rate',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'status',
        'notes',
        'terms_conditions',
        'converted_from_id',
        'ai_generated',
        'invoice_items'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Soft Delete Configuration
    protected $deletedByField = 'deleted_by';
    protected $isDeletedField = 'is_deleted';

    // Validation
    protected $validationRules = [
        'invoice_type' => [
            'label' => 'Invoice Type',
            'rules' => 'required|in_list[invoice,quotation]',
            'errors' => [
                'required' => 'Invoice type is required.',
                'in_list' => 'Invoice type must be either invoice or quotation.'
            ]
        ],
        'client_id' => [
            'label' => 'Client',
            'rules' => 'required|integer|greater_than[0]',
            'errors' => [
                'required' => 'Client is required.',
                'integer' => 'Invalid client selected.',
                'greater_than' => 'Please select a valid client.'
            ]
        ],
        'account_id' => [
            'label' => 'Account',
            'rules' => 'permit_empty|integer|greater_than[0]',
            'errors' => [
                'integer' => 'Invalid account selected.',
                'greater_than' => 'Please select a valid account.'
            ]
        ],
        'issue_date' => [
            'label' => 'Issue Date',
            'rules' => 'required|valid_date',
            'errors' => [
                'required' => 'Issue date is required.',
                'valid_date' => 'Please enter a valid issue date.'
            ]
        ],
        'total_amount' => [
            'label' => 'Total Amount',
            'rules' => 'required|decimal|greater_than_equal_to[0]',
            'errors' => [
                'required' => 'Total amount is required.',
                'decimal' => 'Total amount must be a valid number.',
                'greater_than_equal_to' => 'Total amount cannot be negative.'
            ]
        ],
        'status' => [
            'label' => 'Status',
            'rules' => 'required|in_list[draft,sent,viewed,accepted,paid,overdue,cancelled]',
            'errors' => [
                'required' => 'Status is required.',
                'in_list' => 'Invalid status selected.'
            ]
        ]
    ];

    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setUserAndNumber', 'encodeJsonFields'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = ['encodeJsonFields'];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = ['decodeJsonFields'];
    protected $beforeDelete   = ['setSoftDeleteFields'];
    protected $afterDelete    = [];

    /**
     * Set user_id and generate invoice number before insert
     */
    protected function setUserAndNumber(array $data)
    {
        if (!isset($data['data']['user_id'])) {
            $data['data']['user_id'] = session()->get('user_id');
        }

        // Set default account if not provided
        if (!isset($data['data']['account_id']) || empty($data['data']['account_id'])) {
            $accountModel = new \App\Models\AccountModel();
            $defaultAccount = $accountModel->getDefaultAccount();
            if ($defaultAccount) {
                $data['data']['account_id'] = $defaultAccount['id'];
            }
        }

        if (!isset($data['data']['invoice_number'])) {
            $data['data']['invoice_number'] = $this->generateInvoiceNumber($data['data']['invoice_type'] ?? 'invoice');
        }

        return $data;
    }

    /**
     * Encode JSON fields before insert/update
     */
    protected function encodeJsonFields(array $data)
    {
        if (isset($data['data']['invoice_items']) && is_array($data['data']['invoice_items'])) {
            $data['data']['invoice_items'] = json_encode($data['data']['invoice_items']);
        }

        return $data;
    }

    /**
     * Decode JSON fields after find
     */
    protected function decodeJsonFields(array $data)
    {
        if (isset($data['data'])) {
            // Single record
            if (isset($data['data']['invoice_items']) && is_string($data['data']['invoice_items'])) {
                $data['data']['invoice_items'] = json_decode($data['data']['invoice_items'], true) ?? [];
            }
        } else {
            // Multiple records
            foreach ($data as &$record) {
                if (isset($record['invoice_items']) && is_string($record['invoice_items'])) {
                    $record['invoice_items'] = json_decode($record['invoice_items'], true) ?? [];
                }
            }
        }

        return $data;
    }

    /**
     * Set soft delete fields before delete
     */
    protected function setSoftDeleteFields(array $data)
    {
        $data['data'][$this->deletedByField] = session()->get('user_id');
        $data['data'][$this->isDeletedField] = true;

        return $data;
    }

    /**
     * Override delete method to handle soft deletes
     */
    public function delete($id = null, bool $purge = false)
    {
        if ($purge) {
            return parent::delete($id, true);
        }

        // Soft delete
        $updateData = [
            $this->deletedField => date($this->dateFormat === 'int' ? 'U' : 'Y-m-d H:i:s'),
            $this->deletedByField => session()->get('user_id'),
            $this->isDeletedField => true
        ];

        return $this->update($id, $updateData);
    }

    /**
     * Generate unique invoice number
     */
    public function generateInvoiceNumber(string $type = 'invoice'): string
    {
        $prefix = $type === 'quotation' ? 'QUO' : 'INV';
        $year = date('Y');
        
        $lastInvoice = $this->where('invoice_type', $type)
                           ->where('YEAR(created_at)', $year)
                           ->orderBy('id', 'DESC')
                           ->first();
        
        $nextNumber = 1;
        if ($lastInvoice && !empty($lastInvoice['invoice_number'])) {
            $lastNumber = intval(substr($lastInvoice['invoice_number'], -4));
            $nextNumber = $lastNumber + 1;
        }
        
        return $prefix . '-' . $year . '-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate invoice totals
     */
    public function calculateTotals(array $items, float $taxRate = 0, float $discountAmount = 0): array
    {
        $subtotal = 0;
        
        foreach ($items as $item) {
            $lineTotal = floatval($item['quantity']) * floatval($item['unit_price']);
            $subtotal += $lineTotal;
        }
        
        $taxableAmount = $subtotal - $discountAmount;
        $taxAmount = $taxableAmount * ($taxRate / 100);
        $total = $subtotal + $taxAmount - $discountAmount;
        
        return [
            'subtotal' => round($subtotal, 2),
            'tax_amount' => round($taxAmount, 2),
            'total_amount' => round($total, 2)
        ];
    }

    /**
     * Get invoices with client information
     */
    public function getInvoicesWithClient(array $filters = [], int $perPage = 20): array
    {
        $builder = $this->select('invoices.*, clients.name as client_name, clients.email as client_email')
                        ->join('clients', 'clients.id = invoices.client_id', 'left')
                        ->where('invoices.is_deleted', false);

        // Apply filters
        if (!empty($filters['type'])) {
            $builder->where('invoices.invoice_type', $filters['type']);
        }

        if (!empty($filters['status'])) {
            $builder->where('invoices.status', $filters['status']);
        }

        if (!empty($filters['client_id'])) {
            $builder->where('invoices.client_id', $filters['client_id']);
        }

        if (!empty($filters['search'])) {
            $builder->groupStart()
                    ->like('invoices.invoice_number', $filters['search'])
                    ->orLike('clients.name', $filters['search'])
                    ->groupEnd();
        }

        if (!empty($filters['date_from'])) {
            $builder->where('invoices.issue_date >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $builder->where('invoices.issue_date <=', $filters['date_to']);
        }

        // Filter by user if not admin
        if (session()->get('role') !== 'admin') {
            $builder->where('invoices.user_id', session()->get('user_id'));
        }

        $builder->orderBy('invoices.created_at', 'DESC');

        return [
            'data' => $builder->paginate($perPage),
            'pager' => $this->pager
        ];
    }

    /**
     * Get invoice with all related data
     */
    public function getInvoiceWithDetails(int $invoiceId): array|null
    {
        $invoice = $this->select('invoices.*,
                                 clients.name as client_name, clients.email as client_email,
                                 clients.phone as client_phone, clients.address as client_address,
                                 users.first_name, users.last_name,
                                 accounts.trading_name, accounts.account_name, accounts.account_number,
                                 accounts.bank, accounts.bank_branch, accounts.swift_code,
                                 accounts.logo, accounts.stamp, accounts.signature,
                                 accounts.address as account_address, accounts.phone as account_phone,
                                 accounts.email as account_email, accounts.website as account_website,
                                 accounts.tax_number, accounts.registration_number')
                        ->join('clients', 'clients.id = invoices.client_id', 'left')
                        ->join('users', 'users.id = invoices.user_id', 'left')
                        ->join('accounts', 'accounts.id = invoices.account_id', 'left')
                        ->where('invoices.id', $invoiceId)
                        ->where('invoices.is_deleted', false)
                        ->first();

        if (!$invoice) {
            return null;
        }

        // Invoice items are now stored as JSON in the invoice_items field
        // The decodeJsonFields callback will automatically decode them
        if (!isset($invoice['invoice_items']) || empty($invoice['invoice_items'])) {
            $invoice['invoice_items'] = [];
        }

        // For backward compatibility, also set 'items' key
        $invoice['items'] = $invoice['invoice_items'];

        return $invoice;
    }

    /**
     * Create invoice with items
     */
    public function createInvoiceWithItems(array $invoiceData, array $items): int|false
    {
        try {
            // Calculate totals
            $totals = $this->calculateTotals($items, $invoiceData['tax_rate'] ?? 0, $invoiceData['discount_amount'] ?? 0);

            // Prepare items with calculated line totals
            $processedItems = [];
            foreach ($items as $index => $item) {
                $processedItems[] = [
                    'description' => $item['description'],
                    'quantity' => floatval($item['quantity']),
                    'unit_price' => floatval($item['unit_price']),
                    'line_total' => floatval($item['quantity']) * floatval($item['unit_price']),
                    'sort_order' => $index + 1
                ];
            }

            // Add items and totals to invoice data
            $invoiceData = array_merge($invoiceData, $totals);
            $invoiceData['invoice_items'] = $processedItems;

            // Insert invoice with items as JSON
            if ($this->insert($invoiceData)) {
                return $this->getInsertID();
            }

            return false;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Update invoice with items
     */
    public function updateInvoiceWithItems(int $invoiceId, array $invoiceData, array $items): bool
    {
        try {
            // Calculate totals
            $totals = $this->calculateTotals($items, $invoiceData['tax_rate'] ?? 0, $invoiceData['discount_amount'] ?? 0);

            // Prepare items with calculated line totals
            $processedItems = [];
            foreach ($items as $index => $item) {
                $processedItems[] = [
                    'description' => $item['description'],
                    'quantity' => floatval($item['quantity']),
                    'unit_price' => floatval($item['unit_price']),
                    'line_total' => floatval($item['quantity']) * floatval($item['unit_price']),
                    'sort_order' => $index + 1
                ];
            }

            // Add items and totals to invoice data
            $invoiceData = array_merge($invoiceData, $totals);
            $invoiceData['invoice_items'] = $processedItems;

            // Update invoice with items as JSON
            return $this->update($invoiceId, $invoiceData);

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Convert quotation to invoice
     */
    public function convertQuotationToInvoice(int $quotationId): int|false
    {
        $quotation = $this->getInvoiceWithDetails($quotationId);

        if (!$quotation || $quotation['invoice_type'] !== 'quotation') {
            return false;
        }

        $invoiceData = [
            'invoice_type' => 'invoice',
            'client_id' => $quotation['client_id'],
            'issue_date' => date('Y-m-d'),
            'due_date' => date('Y-m-d', strtotime('+30 days')),
            'subtotal' => $quotation['subtotal'],
            'tax_rate' => $quotation['tax_rate'],
            'tax_amount' => $quotation['tax_amount'],
            'discount_amount' => $quotation['discount_amount'],
            'total_amount' => $quotation['total_amount'],
            'status' => 'draft',
            'notes' => $quotation['notes'],
            'terms_conditions' => $quotation['terms_conditions'],
            'converted_from_id' => $quotationId,
            'invoice_items' => $quotation['invoice_items'] // Copy items as JSON
        ];

        // Insert the new invoice directly (items are already in JSON format)
        if ($this->insert($invoiceData)) {
            $invoiceId = $this->getInsertID();

            // Update quotation status
            $this->update($quotationId, ['status' => 'accepted']);

            return $invoiceId;
        }

        return false;
    }

    /**
     * Get dashboard statistics
     */
    public function getDashboardStats(): array
    {
        $userId = session()->get('user_id');
        $isAdmin = session()->get('role') === 'admin';

        // Base query conditions
        $baseConditions = ['is_deleted' => false];
        if (!$isAdmin) {
            $baseConditions['user_id'] = $userId;
        }

        // Create fresh query builder instances for each statistic to ensure independence
        $stats = [
            'total_invoices' => $this->db->table($this->table)->where($baseConditions)->where('invoice_type', 'invoice')->countAllResults(),
            'total_quotations' => $this->db->table($this->table)->where($baseConditions)->where('invoice_type', 'quotation')->countAllResults(),
            'draft_invoices' => $this->db->table($this->table)->where($baseConditions)->where('status', 'draft')->where('invoice_type', 'invoice')->countAllResults(),
            'pending_quotations' => $this->db->table($this->table)->where($baseConditions)->where('status', 'sent')->where('invoice_type', 'quotation')->countAllResults(),
            'paid_invoices' => $this->db->table($this->table)->where($baseConditions)->where('status', 'paid')->countAllResults(),
            'overdue_invoices' => $this->db->table($this->table)->where($baseConditions)->where('status', 'overdue')->countAllResults()
        ];

        return $stats;
    }
}

<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class AdminUserSeeder extends Seeder
{
    public function run()
    {
        $data = [
            'email'         => '<EMAIL>',
            'password_hash' => password_hash('dakoii', PASSWORD_DEFAULT),
            'first_name'    => 'Admin',
            'last_name'     => 'User',
            'role'          => 'admin',
            'status'        => 'active',
            'created_at'    => date('Y-m-d H:i:s'),
            'updated_at'    => date('Y-m-d H:i:s'),
        ];

        // Check if admin user already exists
        $builder = $this->db->table('users');
        $existingUser = $builder->where('email', $data['email'])->get()->getRow();
        
        if (!$existingUser) {
            $builder->insert($data);
            echo "Admin user created successfully with email: {$data['email']}\n";
        } else {
            echo "Admin user already exists with email: {$data['email']}\n";
        }
    }
}

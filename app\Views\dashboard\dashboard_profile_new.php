<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header">
    <h1 class="page-title">Profile Settings</h1>
    <p class="page-subtitle">Manage your account information and preferences</p>
</div>

<!-- Profile Form -->
<div class="admin-card">
    <div class="admin-card-header">
        <h5 class="admin-card-title">Personal Information</h5>
    </div>
    <div class="admin-card-body">
        <form method="POST" action="<?= base_url('dashboard/updateProfile') ?>">
            <?= csrf_field() ?>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="first_name" class="form-label">First Name</label>
                        <input type="text" class="form-control <?= session('validation') && session('validation')->hasError('first_name') ? 'is-invalid' : '' ?>" 
                               id="first_name" name="first_name" value="<?= old('first_name', $user['first_name'] ?? '') ?>" required>
                        <?php if (session('validation') && session('validation')->hasError('first_name')): ?>
                            <div class="invalid-feedback">
                                <?= session('validation')->getError('first_name') ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="last_name" class="form-label">Last Name</label>
                        <input type="text" class="form-control <?= session('validation') && session('validation')->hasError('last_name') ? 'is-invalid' : '' ?>" 
                               id="last_name" name="last_name" value="<?= old('last_name', $user['last_name'] ?? '') ?>" required>
                        <?php if (session('validation') && session('validation')->hasError('last_name')): ?>
                            <div class="invalid-feedback">
                                <?= session('validation')->getError('last_name') ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" class="form-control <?= session('validation') && session('validation')->hasError('email') ? 'is-invalid' : '' ?>" 
                       id="email" name="email" value="<?= old('email', $user['email'] ?? '') ?>" required>
                <?php if (session('validation') && session('validation')->hasError('email')): ?>
                    <div class="invalid-feedback">
                        <?= session('validation')->getError('email') ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="mb-3">
                <label for="role" class="form-label">Role</label>
                <input type="text" class="form-control" id="role" value="<?= ucfirst($user['role'] ?? '') ?>" readonly>
                <div class="form-text">Your role is managed by system administrators.</div>
            </div>

            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Update Profile
                </button>
                <a href="<?= base_url('dashboard') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Dashboard
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Security Settings -->
<div class="admin-card">
    <div class="admin-card-header">
        <h5 class="admin-card-title">Security Settings</h5>
    </div>
    <div class="admin-card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>Password</h6>
                <p class="text-muted">Keep your account secure by using a strong password.</p>
                <a href="<?= base_url('dashboard/change-password') ?>" class="btn btn-outline-warning">
                    <i class="fas fa-key"></i>
                    Change Password
                </a>
            </div>
            <div class="col-md-6">
                <h6>Account Status</h6>
                <p class="text-muted">Your account is currently <strong><?= ucfirst($user['status'] ?? 'active') ?></strong>.</p>
                <span class="status-badge status-<?= $user['status'] ?? 'active' ?>">
                    <?= ucfirst($user['status'] ?? 'Active') ?>
                </span>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

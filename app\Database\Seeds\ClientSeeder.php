<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class ClientSeeder extends Seeder
{
    public function run()
    {
        $data = [
            [
                'name' => 'Acme Corporation',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '123 Business Ave\nSuite 100',
                'city' => 'New York',
                'state' => 'NY',
                'zip_code' => '10001',
                'country' => 'United States',
                'tax_id' => 'US123456789',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'is_deleted' => false
            ],
            [
                'name' => 'Tech Solutions Ltd',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '456 Innovation Drive',
                'city' => 'San Francisco',
                'state' => 'CA',
                'zip_code' => '94105',
                'country' => 'United States',
                'tax_id' => 'US987654321',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'is_deleted' => false
            ],
            [
                'name' => 'Global Manufacturing Inc',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '789 Industrial Blvd',
                'city' => 'Chicago',
                'state' => 'IL',
                'zip_code' => '60601',
                'country' => 'United States',
                'tax_id' => 'US555123789',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'is_deleted' => false
            ],
            [
                'name' => 'Creative Design Studio',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'address' => '321 Art District',
                'city' => 'Austin',
                'state' => 'TX',
                'zip_code' => '78701',
                'country' => 'United States',
                'tax_id' => 'US246813579',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'is_deleted' => false
            ],
            [
                'name' => 'International Trading Co',
                'email' => '<EMAIL>',
                'phone' => '+44 20 7123 4567',
                'address' => '100 London Bridge Street',
                'city' => 'London',
                'state' => 'England',
                'zip_code' => 'SE1 9RT',
                'country' => 'United Kingdom',
                'tax_id' => 'GB123456789',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'is_deleted' => false
            ]
        ];

        // Using Query Builder to insert data
        $this->db->table('clients')->insertBatch($data);
        
        echo "ClientSeeder: Inserted " . count($data) . " sample clients.\n";
    }
}

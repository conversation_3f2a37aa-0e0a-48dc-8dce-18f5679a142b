<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateAccountsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            // Core business information
            'trading_name' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'comment'    => 'Business trading name',
            ],
            'account_name' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'comment'    => 'Official account name',
            ],
            
            // Banking information
            'account_number' => [
                'type'       => 'VARCHAR',
                'constraint' => 100,
                'comment'    => 'Bank account number',
            ],
            'bank' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'comment'    => 'Bank name',
            ],
            'bank_branch' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'null'       => true,
                'comment'    => 'Bank branch information',
            ],
            'swift_code' => [
                'type'       => 'VARCHAR',
                'constraint' => 20,
                'null'       => true,
                'comment'    => 'International bank identifier',
            ],
            
            // File uploads
            'logo' => [
                'type'       => 'VARCHAR',
                'constraint' => 500,
                'null'       => true,
                'comment'    => 'File path to company logo image',
            ],
            'stamp' => [
                'type'       => 'VARCHAR',
                'constraint' => 500,
                'null'       => true,
                'comment'    => 'File path to company stamp image',
            ],
            'signature' => [
                'type'       => 'VARCHAR',
                'constraint' => 500,
                'null'       => true,
                'comment'    => 'File path to authorized signature image',
            ],
            
            // Contact information
            'address' => [
                'type'    => 'TEXT',
                'null'    => true,
                'comment' => 'Complete business address',
            ],
            'phone' => [
                'type'       => 'VARCHAR',
                'constraint' => 50,
                'null'       => true,
                'comment'    => 'Contact phone number',
            ],
            'email' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'null'       => true,
                'comment'    => 'Contact email address',
            ],
            'website' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'null'       => true,
                'comment'    => 'Company website URL',
            ],
            
            // Legal information
            'tax_number' => [
                'type'       => 'VARCHAR',
                'constraint' => 100,
                'null'       => true,
                'comment'    => 'Tax identification number',
            ],
            'registration_number' => [
                'type'       => 'VARCHAR',
                'constraint' => 100,
                'null'       => true,
                'comment'    => 'Business registration number',
            ],
            
            // Status and default settings
            'is_default' => [
                'type'    => 'BOOLEAN',
                'default' => false,
                'comment' => 'Mark one account as default',
            ],
            'status' => [
                'type'    => 'ENUM',
                'constraint' => ['active', 'inactive'],
                'default' => 'active',
                'comment' => 'Account status',
            ],
            
            // Timestamps
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            
            // Soft delete fields
            'is_deleted' => [
                'type'    => 'BOOLEAN',
                'default' => false,
                'comment' => 'Soft delete flag',
            ],
            'deleted_at' => [
                'type'    => 'TIMESTAMP',
                'null'    => true,
                'comment' => 'Soft delete timestamp',
            ],
            'deleted_by' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => true,
                'comment'    => 'User who deleted the record',
            ],
        ]);
        
        // Primary key
        $this->forge->addPrimaryKey('id');
        
        // Indexes for performance
        $this->forge->addKey('trading_name');
        $this->forge->addKey('account_name');
        $this->forge->addKey('status');
        $this->forge->addKey('is_default');
        $this->forge->addKey('is_deleted');
        $this->forge->addKey('deleted_at');
        $this->forge->addKey('created_at');
        
        // Unique constraints
        $this->forge->addUniqueKey(['trading_name', 'is_deleted'], 'unique_trading_name_not_deleted');
        $this->forge->addUniqueKey(['account_number', 'bank', 'is_deleted'], 'unique_account_bank_not_deleted');
        
        // Foreign key for deleted_by
        $this->forge->addForeignKey('deleted_by', 'users', 'id', 'SET NULL', 'SET NULL');
        
        $this->forge->createTable('accounts');
    }

    public function down()
    {
        $this->forge->dropTable('accounts');
    }
}
